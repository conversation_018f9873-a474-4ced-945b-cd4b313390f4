import { NextResponse } from 'next/server';
import { authConfig } from '@/lib/auth';
import { setAuthCookies } from 'next-firebase-auth-edge/next/cookies';

export async function POST(req) {
    try {
        const authHeader = req.headers.get('authorization');
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            return new NextResponse('Missing or invalid authorization header', { status: 401 });
        }
        
        // Set auth cookies just like in login route
        const response = await setAuthCookies(req.headers, authConfig);
        
        // You could add additional signup-specific logic here if needed
        // For example, storing additional user data in your database
        
        return response;
    } catch (e) {
        console.error('Signup error:', e);
        return new NextResponse('Invalid token', { status: 401 });
    }
}