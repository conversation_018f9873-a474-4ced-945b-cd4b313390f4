'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { auth } from '@/lib/firebase';
import { ThemedButton } from '@/app/theme/ThemeProvider';
import AudioPlayer from '@/app/components/AudioPlayer';
import { ArrowLeft, Volume2, VolumeX } from 'lucide-react';

export default function StoryReaderPage() {
  const params = useParams();
  const router = useRouter();
  const [user, setUser] = useState(null);
  const [authLoading, setAuthLoading] = useState(true);
  
  const [storyData, setStoryData] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [fetchError, setFetchError] = useState(null);
  const [currentSection, setCurrentSection] = useState(0);
  const [storyText, setStoryText] = useState('');
  const [storySections, setStorySections] = useState([]);
  const [isAudioEnabled, setIsAudioEnabled] = useState(false);

  const chatId = params?.chatId;

  // Function to parse story text into readable sections
  const parseStoryIntoSections = (text) => {
    const sections = [];

    // Split by major headings (marked with **)
    const parts = text.split(/\*\*(.*?)\*\*/g);

    let currentSection = null;
    for (let i = 0; i < parts.length; i++) {
      const part = parts[i].trim();
      if (!part) continue;

      // If this is a heading (odd indices after split)
      if (i % 2 === 1) {
        if (currentSection) {
          sections.push(currentSection);
        }
        currentSection = {
          title: part,
          content: '',
          type: 'section'
        };
      } else {
        // This is content
        if (currentSection) {
          currentSection.content += part;
        } else {
          // Content without a heading, create a default section
          sections.push({
            title: 'Story',
            content: part,
            type: 'section'
          });
        }
      }
    }

    // Add the last section
    if (currentSection) {
      sections.push(currentSection);
    }

    return sections.length > 0 ? sections : [{
      title: 'Story',
      content: text,
      type: 'section'
    }];
  };

  // Check authentication state
  useEffect(() => {
    const unsubscribe = auth.onAuthStateChanged((currentUser) => {
      setUser(currentUser);
      setAuthLoading(false);
    });

    return () => unsubscribe();
  }, []);

  // Fetch story data from Firestore
  useEffect(() => {
    const fetchStoryData = async () => {
      if (!chatId) {
        setFetchError('No chat ID provided');
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        const response = await fetch(`/api/chats/${chatId}/storyData`);
        
        if (!response.ok) {
          if (response.status === 404) {
            setFetchError('Story not found');
          } else {
            setFetchError('Failed to load story data');
          }
          setIsLoading(false);
          return;
        }

        const data = await response.json();
        setStoryData(data);
        setFetchError(null);

        // Process the new story format
        if (data.storyText) {
          setStoryText(data.storyText);
          // Parse the story text into sections
          const sections = parseStoryIntoSections(data.storyText);
          setStorySections(sections);
        }
      } catch (error) {
        console.error('Error fetching story data:', error);
        setFetchError('Failed to load story data');
      } finally {
        setIsLoading(false);
      }
    };

    fetchStoryData();
  }, [chatId]);

  // Navigate between story sections
  const handleNavigate = (index) => {
    if (index >= 0 && index < storySections.length) {
      setCurrentSection(index);
    }
  };

  // Get current section content for TTS
  const getCurrentSectionText = () => {
    if (storySections.length > 0 && storySections[currentSection]) {
      const section = storySections[currentSection];
      return `${section.title}. ${section.content}`;
    }
    return storyText;
  };

  // Redirect to login if not authenticated
  if (authLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-app-pattern">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#8B5CF6]"></div>
      </div>
    );
  }

  if (!authLoading && !user) {
    router.push('/register');
    return null;
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-app-pattern">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#8B5CF6] mx-auto mb-4"></div>
          <p className="text-gray-600">Loading your story...</p>
        </div>
      </div>
    );
  }

  if (fetchError) {
    return (
      <div className="min-h-screen bg-app-pattern flex items-center justify-center p-4">
        <div className="bg-white rounded-lg shadow-lg p-6 sm:p-8 max-w-md w-full text-center">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <span className="text-2xl">⚠️</span>
          </div>
          <h1 className="text-xl sm:text-2xl font-bold text-red-600 mb-4">Error Loading Story</h1>
          <p className="text-gray-700 mb-6">{fetchError}</p>
          <div className="space-y-3">
            <ThemedButton onClick={() => window.location.reload()} variant="primary">
              Try Again
            </ThemedButton>
            <ThemedButton onClick={() => router.back()} variant="secondary">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Go Back
            </ThemedButton>
          </div>
        </div>
      </div>
    );
  }

  if (!storyData || (!storyData.storyText && (!storyData.questions || storyData.questions.length === 0))) {
    return (
      <div className="min-h-screen bg-app-pattern flex items-center justify-center p-4">
        <div className="bg-white rounded-lg shadow-lg p-8 max-w-md w-full text-center">
          <h1 className="text-2xl font-bold text-gray-800 mb-4">No Story Found</h1>
          <p className="text-gray-600 mb-6">This story doesn&apos;t have any content yet.</p>
          <ThemedButton onClick={() => router.back()}>
            <ArrowLeft className="w-4 h-4 mr-2" />
            Go Back
          </ThemedButton>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-app-pattern">
      <div className="container mx-auto px-4 py-6 sm:py-8 max-w-5xl">
        {/* Header */}
        <div className="flex items-center justify-between mb-6 sm:mb-8">
          <ThemedButton
            variant="ghost"
            onClick={() => router.back()}
            className="flex items-center"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            <span className="hidden sm:inline">Back</span>
          </ThemedButton>

          <h1 className="text-xl sm:text-2xl lg:text-3xl font-bold text-gray-800 text-center">
            Story Reader
          </h1>

          <div className="w-16 sm:w-20"></div> {/* Spacer for centering */}
        </div>

        {/* Audio Controls */}
        <div className="bg-white rounded-lg shadow-lg p-4 sm:p-6 mb-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-gray-800">Audio Controls</h2>
            <button
              onClick={() => setIsAudioEnabled(!isAudioEnabled)}
              className="flex items-center px-3 py-2 bg-app-accent text-white rounded-md hover:bg-app-accent-hover transition-colors"
            >
              {isAudioEnabled ? <VolumeX className="w-4 h-4 mr-2" /> : <Volume2 className="w-4 h-4 mr-2" />}
              {isAudioEnabled ? 'Disable Audio' : 'Enable Audio'}
            </button>
          </div>

          {isAudioEnabled && (
            <AudioPlayer
              text={getCurrentSectionText()}
              voiceGender="FEMALE"
            />
          )}
        </div>

        {/* Story Content */}
        <div className="bg-white rounded-lg shadow-lg p-4 sm:p-6 lg:p-8">
          {storySections.length > 0 ? (
            <div>
              {/* Section Navigation */}
              {storySections.length > 1 && (
                <div className="flex flex-wrap gap-2 mb-6 pb-4 border-b">
                  {storySections.map((section, index) => (
                    <button
                      key={index}
                      onClick={() => handleNavigate(index)}
                      className={`px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                        currentSection === index
                          ? 'bg-app-accent text-white'
                          : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                      }`}
                    >
                      {section.title}
                    </button>
                  ))}
                </div>
              )}

              {/* Current Section */}
              <div className="prose max-w-none">
                <h2 className="text-2xl font-bold text-gray-800 mb-4">
                  {storySections[currentSection]?.title}
                </h2>
                <div className="text-gray-700 leading-relaxed whitespace-pre-wrap">
                  {storySections[currentSection]?.content}
                </div>
              </div>

              {/* Navigation Buttons */}
              {storySections.length > 1 && (
                <div className="flex justify-between mt-8 pt-4 border-t">
                  <button
                    onClick={() => handleNavigate(currentSection - 1)}
                    disabled={currentSection === 0}
                    className="flex items-center px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <ArrowLeft className="w-4 h-4 mr-2" />
                    Previous
                  </button>
                  <span className="text-sm text-gray-500 self-center">
                    {currentSection + 1} of {storySections.length}
                  </span>
                  <button
                    onClick={() => handleNavigate(currentSection + 1)}
                    disabled={currentSection === storySections.length - 1}
                    className="flex items-center px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Next
                    <ArrowLeft className="w-4 h-4 ml-2 rotate-180" />
                  </button>
                </div>
              )}
            </div>
          ) : (
            <div className="prose max-w-none">
              <div className="text-gray-700 leading-relaxed whitespace-pre-wrap">
                {storyText}
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="mt-8 text-center text-sm text-gray-500">
          <p>Use the audio controls above to listen to the story content</p>
        </div>
      </div>
    </div>
  );
}
