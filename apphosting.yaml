# Firebase App Hosting configuration
runtime: nodejs20
runtime_config:
  nodejs_version: 20.9.0

env_variables:
  # Firebase Configuration
  NEXT_PUBLIC_FIREBASE_API_KEY: AIzaSyAxYGR4YbpcThPO9IcCNRPX15TlIzu4nGw
  NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN: the-money-tales.firebaseapp.com
  NEXT_PUBLIC_FIREBASE_PROJECT_ID: the-money-tales
  NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET: the-money-tales.appspot.com
  NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID: 34776109288
  NEXT_PUBLIC_FIREBASE_APP_ID: 1:34776109288:web:bc9dd0feae7ac9ae5e395d
  
  # Firebase Admin
  FIREBASE_CLIENT_EMAIL: '<EMAIL>'
  FIREBASE_PROJECT_ID: the-money-tales
  FIREBASE_PRIVATE_KEY: '-----B<PERSON><PERSON> PRIVATE KEY-----\nMIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQDjN4HOOVAx430o\nMe14yvimAppx72Av1kShXL9B7s2xPwNJMSF9xwlcE/5I+D+nFoT0FLa2mjsOPKU8\npIHToVc5Pw7sn3plztiT0orQmUax4Lkg1UNrwNAq73gb4h4IP+H9TmrOfQsVSRkq\nmTc56T7wbjg+CJnrZYTI0k7QdlgH56oPvLCcO79jkNLiT0TA9LCyhoCQqxjDoJDy\ncQXSAeYtAKAaigm9dIqqB9XKylOWgrBoiXFG/xlApy9pv3aJfenTav9O2j1/vfzt\nDGhbMD21TRIbJUehFHsBYO4LWWJhoozoskfvRrlrs0qYCvy8mY8iSdF/NleQI7Hw\nejEO/zGHAgMBAAECggEAGrt4l6kxkLUJkBWTsV778xgiUJ5PrEEi0PNHAzxT0fMu\ncUmI/bHRnZYymCYw4vkoyki3mHM72Hh1HlXiemIkj3UmTduTfG/iFj4ym8k0wo/E\naCL/LCnCtBJ5iJpJW0Ic1f8IXOPkTmxLvDHLiBIzl/j3wACmD0Zh4Xiw0iDiSYeU\nvqEYs2jqQ4HzoG6LBPdxLFsQz2h/rto803GE3/Sf1aB34OrKPTqAucfudEhQ6GKX\nowhIu0yRVz3gP5LqHj/Bs0wq8rQ1j9XFoHKgXVH6yDH94v8PFJWXAGT1R0FjAQNo\nqz2v1tps+95jUSrnhlsGoeQ0Z0i7D/ZvWEcD5a79mQKBgQD0P8w+fD2kfw+PVd40\nLaNHOzW0FWy7Y+iPGcWufQka5ncrl+A+z/EyWgQpxkzEerUuG+PCzwRALj4MUEz5\nmbOdeljsALJ3rsMJ+k6DX2VX62TD6nIh1dLd4XxNM6v8z/GzWMd5ONS3+54JGIq3\n7gra6cF93GTX9bt5UHTaplKqzwKBgQDuJe+0VBk1UtOIXM76DEW0LRX10XDk9QMJ\nYIjvEELsXntT5M0dJo/QSxQmDx9gb+9wro+cQUD/fgrtoU1hBoES9Ra3KKppZgb4\nHs7u3N7daobwer2rghdYbmY5eqysn6Np7w+TUFvMmsBNv1+TVhnNTqquMYOL5duJ\nTDEXXojbyQKBgEb1wgDlV4NS5tJdwBCosacgLJoFH3HRtN7ePfBTozGNg1HG7f9R\nqmFWhZ8dXXA3tiuPLryKKFH9HUm2iqZ5YRV/VhY6nZ7nxwI/FlBUhrzkRoIkusPZ\nu0l7GQHU4UKxpybTxCATwrkxANjjqGmHXt/6yZwnS6TyX2T/WvOrHplBAoGBAIeq\nCNF7jxi3ZFWsOSRs1VB81qPAekWT5fTzj05Qvy8T68Gw98ABda+89W9w8fjoC0jv\nyFPFQJfLX8JbP09keycng5c3jUyZmKLEOjIMlNEhnFlmMqJ/a3ku+wEHh1KXZmo0\ni3TqNEFsbtxbhsDSLcStacHt5vhNKzEdvGGJswExAoGAPXTuE21ZWQ19vJk/ecd+\nH8ravEzlcPLJrcuJLPxC0JxGs07cS/k/fi4/hj7iDI+fvqL81Q/Yu805nO0P6IsB\nUWqDChxekw0nmwyenhKAME4agkEVZCsY3oIodJJTgcgm4AaUhOu/hRSjTkQS+qsH\npM6jXOuveMxc1eTZy9wxYCA=\n-----END PRIVATE KEY-----\n'
  
  # Email Configuration
  EMAIL_HOST: smtpout.secureserver.net
  EMAIL_PORT: 587
  EMAIL_USER: <EMAIL>
  EMAIL_PASSWORD: Let3..Us6..Get9..Rich0
  EMAIL_FROM: '<EMAIL>'
  EMAIL_SECURE: false
  
  # API Keys
  FREEPIK_API_KEY: FPSX2de400a58eeb4b41b4d6b28c48460a86
  GEMINI_API_KEY: AIzaSyDg3iQLH41DgQ1Ak5S1a6RPXsk6padN6vI
  
  # EmailJS Configuration
  NEXT_PUBLIC_EMAILJS_SERVICE_ID: service_najtc4v
  NEXT_PUBLIC_EMAILJS_TEMPLATE_ID: template_kokxtyk
  NEXT_PUBLIC_EMAILJS_PUBLIC_KEY: AxXalbUimOXNFsvW5
  
  # Auth Configuration
  COOKIE_SIGNATURE_KEY: nPiBqKuPC6mXbMy6yR5ULHyt6Syo1lNjfTVr5NMZPsE
  COOKIE_SIGNATURE_KEY_PREVIOUS: sqZMJtvcgKZrzFjHvhdgAa1RgRyh2iQvM7YoZIzr3wY
  USE_SECURE_COOKIES: true
  
  # Environment Settings
  NEXT_PUBLIC_APP_ENV: "production"
  NEXT_PUBLIC_SITE_URL: "https://moneytalesweb-staging--the-money-tales.asia-east1.hosted.app"
  REFERRAL_SECRET: themoneytales2024
  UNSUBSCRIBE_SECRET: moneytales2024unsubscribe

handlers:
  - url: /.*
    script: auto
    secure: always

automatic_scaling:
  min_instances: 0
  max_instances: 10

