// Google Auth Test Script
// Run with: node tests/google-auth-test.js

import { initializeApp } from 'firebase/app';
import { 
  getAuth, 
  signInWithEmailAndPassword, 
  GoogleAuthProvider, 
  signInWithPopup,
  signInWithRedirect,
  getRedirectResult,
  onAuthStateChanged
} from 'firebase/auth';
import fetch from 'node-fetch';
import dotenv from 'dotenv';
import fs from 'fs';

// Load environment variables
dotenv.config();

// Initialize Firebase with your config
const firebaseConfig = {
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID
};

// Log file setup
const logFile = './google-auth-test.log';
fs.writeFileSync(logFile, `=== Google Auth Test Started at ${new Date().toISOString()} ===\n`);

// Custom logger that writes to console and file
const logger = {
  log: (...args) => {
    const message = args.map(arg => 
      typeof arg === 'object' ? JSON.stringify(arg, null, 2) : arg
    ).join(' ');
    console.log(message);
    fs.appendFileSync(logFile, `[LOG] ${message}\n`);
  },
  error: (...args) => {
    const message = args.map(arg => 
      typeof arg === 'object' ? JSON.stringify(arg, null, 2) : arg
    ).join(' ');
    console.error(message);
    fs.appendFileSync(logFile, `[ERROR] ${message}\n`);
  }
};

// Initialize Firebase
logger.log('Initializing Firebase with config:', {
  apiKey: firebaseConfig.apiKey?.substring(0, 5) + '...',
  authDomain: firebaseConfig.authDomain,
  projectId: firebaseConfig.projectId
});

const app = initializeApp(firebaseConfig);
const auth = getAuth(app);

// Test functions
async function testGoogleAuthPopup() {
  logger.log('\n=== Testing Google Auth with Popup ===');
  try {
    const provider = new GoogleAuthProvider();
    provider.addScope('email');
    provider.addScope('profile');
    
    logger.log('Provider configured, initiating popup...');
    const result = await signInWithPopup(auth, provider);
    
    logger.log('Popup auth successful!');
    logger.log('User:', {
      uid: result.user.uid,
      email: result.user.email,
      displayName: result.user.displayName
    });
    
    // Get the Google OAuth access token
    const credential = GoogleAuthProvider.credentialFromResult(result);
    logger.log('Credential:', {
      accessToken: credential.accessToken?.substring(0, 10) + '...',
      providerId: credential.providerId,
      signInMethod: credential.signInMethod
    });
    
    // Get the ID token
    const idToken = await result.user.getIdToken();
    logger.log('ID Token length:', idToken.length);
    logger.log('ID Token first 20 chars:', idToken.substring(0, 20) + '...');
    
    return { success: true, user: result.user, idToken };
  } catch (error) {
    logger.error('Google popup auth error:', error);
    logger.error('Error code:', error.code);
    logger.error('Error message:', error.message);
    if (error.email) {
      logger.error('Email associated with error:', error.email);
    }
    if (error.credential) {
      logger.error('Credential associated with error:', error.credential);
    }
    return { success: false, error };
  }
}

async function testAPICall(idToken) {
  logger.log('\n=== Testing API Call with ID Token ===');
  try {
    logger.log('Making API call to /api/auth/login');
    const response = await fetch('http://localhost:3000/api/auth/login', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${idToken}`,
        'Content-Type': 'application/json'
      }
    });
    
    logger.log('API Response status:', response.status);
    logger.log('API Response status text:', response.statusText);
    
    const responseText = await response.text();
    logger.log('API Response body:', responseText);
    
    return { 
      success: response.ok, 
      status: response.status, 
      body: responseText 
    };
  } catch (error) {
    logger.error('API call error:', error);
    return { success: false, error: error.message };
  }
}

async function testAuthStateListener() {
  logger.log('\n=== Testing Auth State Listener ===');
  return new Promise((resolve) => {
    const unsubscribe = onAuthStateChanged(auth, (user) => {
      if (user) {
        logger.log('Auth state changed: User is signed in');
        logger.log('User:', {
          uid: user.uid,
          email: user.email,
          displayName: user.displayName,
          emailVerified: user.emailVerified,
          isAnonymous: user.isAnonymous,
          providerData: user.providerData
        });
      } else {
        logger.log('Auth state changed: User is signed out');
      }
      unsubscribe();
      resolve(user);
    });
  });
}

// Main test function
async function runTests() {
  logger.log('Starting Google Auth tests...');
  
  // Check initial auth state
  logger.log('\n=== Initial Auth State ===');
  const initialUser = auth.currentUser;
  logger.log('Initial user:', initialUser ? {
    uid: initialUser.uid,
    email: initialUser.email
  } : 'No user signed in');
  
  // Test auth state listener
  await testAuthStateListener();
  
  // Test Google popup auth
  const popupResult = await testGoogleAuthPopup();
  
  // If popup auth was successful, test API call
  if (popupResult.success && popupResult.idToken) {
    await testAPICall(popupResult.idToken);
  }
  
  logger.log('\n=== Tests Completed ===');
}

// Run the tests
runTests().catch(error => {
  logger.error('Unhandled error in test script:', error);
});