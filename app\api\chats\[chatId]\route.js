import { NextResponse } from 'next/server';
import { getFirestore, FieldValue } from 'firebase-admin/firestore';
import { initializeApp as initializeAdminApp, cert, getApps as getAdminApps } from 'firebase-admin/app';

// Initialize Firebase Admin if not already initialized
if (!getAdminApps().length) {
  initializeAdminApp({
    credential: cert({
      projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
      clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
      privateKey: process.env.FIREBASE_PRIVATE_KEY.replace(/\\n/g, '\n'),
    }),
  });
}

const db = getFirestore();

// GET a specific chat
export async function GET(request, { params }) {
  try {
    const { chatId } = await params;
    
    if (!chatId) {
      return NextResponse.json(
        { error: 'Missing chat ID' },
        { status: 400 }
      );
    }

    const chatDoc = await db.collection('chats').doc(chatId).get();

    if (!chatDoc.exists) {
      return NextResponse.json(
        { error: 'Chat not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      id: chatDoc.id,
      ...chatDoc.data()
    });
  } catch (error) {
    console.error('Error fetching chat:', error);
    return NextResponse.json(
      { error: 'Failed to fetch chat' },
      { status: 500 }
    );
  }
}

// PUT update a chat
export async function PUT(request, { params }) {
  try {
    const { chatId } = await params;
    const { title, bookInfo } = await request.json();
    
    if (!chatId) {
      return NextResponse.json(
        { error: 'Missing chat ID' },
        { status: 400 }
      );
    }

    const chatRef = db.collection('chats').doc(chatId);
    const chatDoc = await chatRef.get();

    if (!chatDoc.exists) {
      return NextResponse.json(
        { error: 'Chat not found' },
        { status: 404 }
      );
    }

    const updateData = {
      updatedAt: new Date()
    };

    if (title !== undefined) {
      updateData.title = title;
    }

    if (bookInfo !== undefined) {
      updateData.bookInfo = bookInfo;
    }

    await chatRef.update(updateData);
    
    return NextResponse.json({
      success: true,
      id: chatId
    });
  } catch (error) {
    console.error('Error updating chat:', error);
    return NextResponse.json(
      { error: 'Failed to update chat' },
      { status: 500 }
    );
  }
}

// DELETE a chat
export async function DELETE(request, { params }) {
  try {
    const { chatId } = await params;
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    
    if (!chatId) {
      return NextResponse.json(
        { error: 'Missing chat ID' },
        { status: 400 }
      );
    }
    
    if (!userId) {
      return NextResponse.json(
        { error: 'Missing user ID' },
        { status: 400 }
      );
    }

    // Check if user has access to this chat
    const userDoc = await db.collection('users').doc(userId).get();
    
    if (!userDoc.exists) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }
    
    const userData = userDoc.data();
    if (!userData.chats || !userData.chats.includes(chatId)) {
      return NextResponse.json(
        { error: 'Unauthorized access to chat' },
        { status: 403 }
      );
    }

    // Remove chat from user's chats array
    await db.collection('users').doc(userId).update({
      chats: FieldValue.arrayRemove(chatId)
    });
    
    // Delete the chat document
    const chatRef = db.collection('chats').doc(chatId);
    await chatRef.delete();
    
    return NextResponse.json({
      success: true,
      id: chatId
    });
  } catch (error) {
    console.error('Error deleting chat:', error);
    return NextResponse.json(
      { error: 'Failed to delete chat' },
      { status: 500 }
    );
  }
}
