import { NextResponse } from 'next/server';
import { getFirestore, FieldValue } from 'firebase-admin/firestore';
import { initializeApp as initializeAdminApp, cert, getApps as getAdminApps } from 'firebase-admin/app';

// Initialize Firebase Admin if not already initialized
if (!getAdminApps().length) {
  initializeAdminApp({
    credential: cert({
      projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
      clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
      privateKey: process.env.FIREBASE_PRIVATE_KEY.replace(/\\n/g, '\n'),
    }),
  });
}

const db = getFirestore();

// GET all chats for a user
export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');

    if (!userId) {
      return NextResponse.json(
        { error: 'Missing userId parameter' },
        { status: 400 }
      );
    }

    // Get user document to retrieve chat IDs
    const userDoc = await db.collection('users').doc(userId).get();
    
    if (!userDoc.exists) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }
    
    const userData = userDoc.data();
    const chatIds = userData.chats || [];
    
    if (chatIds.length === 0) {
      return NextResponse.json({ chats: [] });
    }
    
    // Fetch all chats by their IDs
    const chats = [];
    for (const chatId of chatIds) {
      const chatDoc = await db.collection('chats').doc(chatId).get();
      if (chatDoc.exists) {
        chats.push({
          id: chatDoc.id,
          ...chatDoc.data()
        });
      }
    }

    return NextResponse.json({ chats });
  } catch (error) {
    console.error('Error fetching chats:', error);
    return NextResponse.json(
      { error: 'Failed to fetch chats' },
      { status: 500 }
    );
  }
}

// POST create a new chat
export async function POST(request) {
  try {
    const { userId, title, bookInfo } = await request.json();

    if (!userId) {
      return NextResponse.json(
        { error: 'Missing required parameters' },
        { status: 400 }
      );
    }

    // Create chat without userId field
    const chatData = {
      title: title || 'New Chat',
      createdAt: new Date(),
      updatedAt: new Date(),
      bookInfo: bookInfo || {}
    };

    // Add chat to Firestore
    const docRef = await db.collection('chats').add(chatData);
    const chatId = docRef.id;
    
    // Update user document to include this chat ID
    // First check if user document exists, create it if it doesn't
    const userRef = db.collection('users').doc(userId);
    const userDoc = await userRef.get();

    if (!userDoc.exists) {
      // Create user document if it doesn't exist
      await userRef.set({
        chats: [chatId],
        createdAt: new Date(),
        updatedAt: new Date()
      });
    } else {
      // Update existing user document
      await userRef.update({
        chats: FieldValue.arrayUnion(chatId),
        updatedAt: new Date()
      });
    }
    
    return NextResponse.json({
      success: true,
      id: chatId,
      ...chatData
    });
  } catch (error) {
    console.error('Error creating chat:', error);
    return NextResponse.json(
      { error: 'Failed to create chat' },
      { status: 500 }
    );
  }
}
