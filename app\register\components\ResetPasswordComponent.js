'use client';
import { useState, useEffect } from 'react';
import { sendPasswordResetEmail } from 'firebase/auth';
import { auth } from '@/lib/firebase';
import Image from 'next/image';

export default function ResetPasswordComponent({ onBack }) {
  const [email, setEmail] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');
    
    try {
      await sendPasswordResetEmail(auth, email);
      setSuccess(true);
    } catch (err) {
      setError(err.message || 'Failed to send reset email');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (error) {
      const timer = setTimeout(() => setError(''), 5000);
      return () => clearTimeout(timer);
    }
  }, [error]);

  return (
    <div className="flex flex-col md:flex-row h-[100dvh] md:min-h-screen bg-app-pattern overflow-hidden">
      <div className="w-full md:w-1/2 p-4 md:p-6 flex flex-col justify-center relative">
        <button
          onClick={() => onBack('login')}
          className="absolute top-4 left-4 md:top-6 md:left-6 text-[#696F79] hover:text-white transition-colors duration-200 z-20 text-sm md:text-base"
        >
          ← Back
        </button>

        <div className="max-w-sm mx-auto w-full space-y-4 mt-12 md:mt-0">
          <div className="space-y-1">
            <h1 className="text-2xl md:text-3xl font-bold text-white font-inter">
              Reset Password
            </h1>
            <p className="text-[#696F79] text-sm font-inter">
              Enter your email to receive a password reset link
            </p>
          </div>

          {error && (
            <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-3">
              <p className="text-red-400 text-sm">{error}</p>
            </div>
          )}

          {success ? (
            <div className="bg-green-500/10 border border-green-500/20 rounded-lg p-4 text-center">
              <p className="text-green-400 text-sm mb-3">Reset link sent! Check your email inbox.</p>
              <button
                onClick={() => onBack('login')}
                className="text-white bg-[#8B5CF6] hover:bg-[#7C3AED] px-4 py-2 rounded-md text-sm transition-colors duration-200"
              >
                Return to Login
              </button>
            </div>
          ) : (
            <form onSubmit={handleSubmit} className="space-y-4">
              <div>
                <label className="block text-[#696F79] text-sm mb-1 font-inter">
                  Your email*
                </label>
                <input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="Enter your email"
                  required
                  disabled={isLoading}
                  className="w-full px-3 py-2.5 bg-[#3a3d42] border border-[#4a4d52] rounded-md text-white placeholder-[#696F79] focus:outline-none focus:border-[#696F79] transition-colors duration-200 text-sm font-inter disabled:opacity-50"
                />
              </div>

              <button
                type="submit"
                disabled={isLoading}
                className="w-full py-2.5 px-4 bg-[#8B5CF6] text-white rounded-md font-medium hover:bg-[#7C3AED] transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed text-sm font-inter"
              >
                {isLoading ? 'Sending...' : 'Send Reset Link'}
              </button>
            </form>
          )}
        </div>
      </div>

      <div className="hidden md:flex md:w-1/2 items-center justify-start p-4">
        <div className="relative w-full h-full max-h-[90vh] rounded-2xl overflow-hidden bg-gradient-to-br from-[#3a3d42] to-[#2a2d32] shadow-2xl">
          <Image
            src="https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=600&h=800&fit=crop&crop=center"
            alt="Classical painting"
            width={600}
            height={800}
            className="w-full h-full object-cover"
            onError={(e) => {
              e.target.style.display = 'none';
              e.target.nextSibling.style.display = 'flex';
            }}
          />
          <div className="absolute inset-0 hidden items-center justify-center">
            <div className="text-center space-y-4 p-8">
              <div className="w-32 h-32 mx-auto bg-[#696F79]/20 rounded-full flex items-center justify-center">
                <svg className="w-16 h-16 text-[#696F79]" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M21 19V5c0-1.1-.9-2-2-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2zM8.5 13.5l2.5 3.01L14.5 12l4.5 6H5l3.5-4.5z"/>
                </svg>
              </div>
              <p className="text-[#696F79] text-sm">Classical painting</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}