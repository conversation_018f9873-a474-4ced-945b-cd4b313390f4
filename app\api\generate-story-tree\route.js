import { NextResponse } from 'next/server';
import { GoogleGenAI } from '@google/genai';

// Initialize the Gemini API client
const apiKey = process.env.GEMINI_API_KEY;
const genAI = new GoogleGenAI({ apiKey });



export async function POST(request) {
  try {
    let whatIfPrompt = '';
    let audioTranscription = '';
    let detectedLanguage = 'en-US';

    const contentType = request.headers.get('content-type');

    if (contentType && contentType.includes('multipart/form-data')) {
      const formData = await request.formData();
      whatIfPrompt = formData.get('whatIfPrompt');
      const audioFile = formData.get('audioFile');

      if (!whatIfPrompt) {
        return NextResponse.json({ error: 'What if prompt is required' }, { status: 400 });
      }

      // Process audio file if provided
      if (audioFile && audioFile.size > 0) {
        try {
          const speechFormData = new FormData();
          speechFormData.append('audioFile', audioFile);

          const speechResponse = await fetch(`${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/api/speech-to-text`, {
            method: 'POST',
            body: speechFormData,
          });

          if (speechResponse.ok) {
            const speechResult = await speechResponse.json();
            audioTranscription = speechResult.transcription || '';
            detectedLanguage = speechResult.detectedLanguage || 'en-US';
          }
        } catch (audioError) {
          // Continue without audio transcription
        }
      }
    } else {
      const body = await request.json();
      whatIfPrompt = body.whatIfPrompt;
      if (!whatIfPrompt) {
        return NextResponse.json({ error: 'What if prompt is required' }, { status: 400 });
      }
    }

    if (!apiKey) {
      return NextResponse.json({ error: 'API key not configured' }, { status: 500 });
    }

    const combinedContext = audioTranscription
      ? `${whatIfPrompt}\n\nAdditional context from audio: ${audioTranscription}`
      : whatIfPrompt;

    const isHindiInput = /[\u0900-\u097F]/.test(combinedContext) || detectedLanguage === 'hi-IN';

    // Generate the story with new format - readable text instead of JSON
    const storyPrompt = `
You are a creative fiction writer specializing in interactive storytelling. Create an engaging "what if" story based on the user's scenario.

User's "What If" scenario: ${combinedContext}

${isHindiInput ?
  `भाषा निर्देश: इनपुट हिंदी में है, इसलिए कृपया सभी कंटेंट केवल हिंदी भाषा में जेनरेट करें। देवनागरी लिपि का उपयोग करें।` :
  `Language Instruction: Generate all content in English language.`
}

Create an interactive story in the following format:

**STORY TITLE**
[Create an engaging title for this story]

**OPENING SCENARIO**
[Write a compelling 300-400 word opening that sets up the alternate scenario. Make it vivid and immersive.]

**CHOICE POINT 1**
Now you must decide what happens next. Choose one of the following options:

A) [First choice option - 30-40 words describing the action]
B) [Second choice option - 30-40 words describing the action]
C) [Third choice option - 30-40 words describing the action]

**SCENARIO A: [Title for choice A]**
[300-400 word continuation if choice A is selected]

**CHOICE POINT 2A**
What happens next?

A1) [Choice option - 30-40 words]
A2) [Choice option - 30-40 words]
A3) [Choice option - 30-40 words]

**SCENARIO B: [Title for choice B]**
[300-400 word continuation if choice B is selected]

**CHOICE POINT 2B**
What happens next?

B1) [Choice option - 30-40 words]
B2) [Choice option - 30-40 words]
B3) [Choice option - 30-40 words]

**SCENARIO C: [Title for choice C]**
[300-400 word continuation if choice C is selected]

**CHOICE POINT 2C**
What happens next?

C1) [Choice option - 30-40 words]
C2) [Choice option - 30-40 words]
C3) [Choice option - 30-40 words]

Continue this pattern for one more level (scenarios A1, A2, A3, B1, B2, B3, C1, C2, C3) to create a rich branching narrative.

Make the story engaging, with meaningful choices that lead to different outcomes. Each scenario should feel complete while building toward the next decision point.`;

    const storyResult = await genAI.models.generateContent({
      model: 'gemini-2.0-flash-001',
      contents: [{ role: 'user', parts: [{ text: storyPrompt }] }],
      config: {
        temperature: 0.7,
        topP: 0.9,
        topK: 40,
        maxOutputTokens: 4096,
      },
    });

    const storyText = storyResult.candidates?.[0]?.content?.parts?.[0]?.text || '';

    if (!storyText) {
      return NextResponse.json({ error: 'Failed to generate story content' }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      storyText: storyText,
      detectedLanguage: detectedLanguage,
      hasAudioTranscription: !!audioTranscription
    });

  } catch (error) {
    console.error('Error generating story tree:', error);
    return NextResponse.json({ error: 'Failed to generate story' }, { status: 500 });
  }
}
