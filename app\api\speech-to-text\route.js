import { NextResponse } from 'next/server';
import { SpeechClient } from '@google-cloud/speech';
import { initializeApp, getApps } from 'firebase/app';
import { getStorage, ref, uploadBytes, getDownloadURL } from 'firebase/storage';

// Firebase configuration
const firebaseConfig = {
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID,
};

// Initialize Firebase
const app = getApps().length === 0 ? initializeApp(firebaseConfig) : getApps()[0];
const storage = getStorage(app);

// Initialize the Speech-to-Text client with v1 API and data logging enabled
let speechClient;

try {
  speechClient = new SpeechClient({
    projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
    keyFilename: process.env.GOOGLE_APPLICATION_CREDENTIALS,
    credentials: process.env.FIREBASE_PRIVATE_KEY ? {
      client_email: process.env.FIREBASE_CLIENT_EMAIL,
      private_key: process.env.FIREBASE_PRIVATE_KEY.replace(/\\n/g, '\n'),
    } : undefined,
    // Enable data logging for cost optimization
    apiEndpoint: 'speech.googleapis.com',
    apiVersion: 'v1',
  });
} catch (error) {
  console.error('Failed to initialize Speech-to-Text client:', error);
}

// Helper function to generate unique filename for transcription
function generateTranscriptionFilename(originalFilename) {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const randomId = Math.random().toString(36).substring(2, 8);
  const baseName = originalFilename ? originalFilename.replace(/\.[^/.]+$/, '') : 'audio';
  return `${baseName}-${timestamp}-${randomId}.txt`;
}

export async function POST(request) {
  try {
    if (!speechClient) {
      return NextResponse.json(
        { error: 'Speech-to-Text service not available' },
        { status: 503 }
      );
    }

    const formData = await request.formData();
    const audioFile = formData.get('audioFile');

    if (!audioFile) {
      return NextResponse.json(
        { error: 'Audio file is required' },
        { status: 400 }
      );
    }

    // Convert file to buffer
    const audioBuffer = Buffer.from(await audioFile.arrayBuffer());
 
    // Detect audio format from file extension
    const fileName = audioFile.name.toLowerCase();
    let encoding = 'LINEAR16'; // Default
    let sampleRateHertz = 16000; // Default

    if (fileName.endsWith('.mp3')) {
      encoding = 'MP3';
    } else if (fileName.endsWith('.wav')) {
      encoding = 'LINEAR16';
    } else if (fileName.endsWith('.m4a') || fileName.endsWith('.aac')) {
      encoding = 'MP3'; // Google Speech-to-Text treats M4A/AAC similar to MP3
    } else if (fileName.endsWith('.ogg')) {
      encoding = 'OGG_OPUS';
    } else if (fileName.endsWith('.flac')) {
      encoding = 'FLAC';
    }

    // Configure the speech recognition request with v1 API and data logging
    const request_config = {
      audio: {
        content: audioBuffer.toString('base64'),
      },
      config: {
        encoding: encoding,
        sampleRateHertz: sampleRateHertz,
        languageCode: 'en-US', 
        alternativeLanguageCodes: ['hi-IN'], 
        enableAutomaticPunctuation: true,
        enableWordTimeOffsets: false,
        model: 'default', 
        useEnhanced: false, 
        
        enableSeparateRecognitionPerChannel: false,
        diarizationConfig: undefined, 
        metadata: {
          interactionType: 'VOICE_SEARCH', 
          industryNaicsCodeOfAudio: 0,
          microphoneDistance: 'NEARFIELD',
          originalMediaType: 'AUDIO',
          recordingDeviceType: 'OTHER_INDOOR_DEVICE',
        },
      },
    };

    console.log('Processing audio file:', fileName, 'with encoding:', encoding);

    // Perform the speech recognition
    const [response] = await speechClient.recognize(request_config);

    if (!response.results || response.results.length === 0) {
      return NextResponse.json(
        { error: 'No speech detected in the audio file' },
        { status: 400 }
      );
    }

    // Extract the transcription
    const transcription = response.results
      .map(result => result.alternatives[0].transcript)
      .join(' ');

    // Detect the language of the transcribed text
    const detectedLanguage = /[\u0900-\u097F]/.test(transcription) ? 'hi-IN' : 'en-US';

    console.log('Transcription successful:', transcription.substring(0, 100) + '...');

    // Store transcription in Firebase Storage as .txt file
    let transcriptionUrl = null;
    let storedFilename = null;
    try {
      const filename = generateTranscriptionFilename(fileName);
      storedFilename = filename;
      const transcriptionRef = ref(storage, `transcriptions/${filename}`);

      // Create a blob with the transcription text
      const transcriptionBlob = new Blob([transcription], { type: 'text/plain' });

      // Upload to Firebase Storage
      await uploadBytes(transcriptionRef, transcriptionBlob);

      // Get the download URL
      transcriptionUrl = await getDownloadURL(transcriptionRef);

      console.log('Transcription stored in Firebase Storage:', transcriptionUrl);
    } catch (storageError) {
      console.error('Error storing transcription in Firebase Storage:', storageError);
      // Continue without storage - don't fail the entire request
    }

    return NextResponse.json({
      success: true,
      transcription: transcription,
      detectedLanguage: detectedLanguage,
      confidence: response.results[0]?.alternatives[0]?.confidence || 0,
      transcriptionUrl: transcriptionUrl,
      metadata: {
        audioFileName: fileName,
        transcriptionFileName: storedFilename,
        encoding: encoding,
        sampleRate: sampleRateHertz,
        storedAt: transcriptionUrl ? new Date().toISOString() : null,
        apiVersion: 'v1',
        dataLoggingEnabled: true
      }
    });

  } catch (error) {
    console.error('Speech-to-Text error:', error);

    // Handle specific Google Cloud errors
    if (error.code === 7) { // PERMISSION_DENIED
      return NextResponse.json(
        { error: 'Authentication failed. Please check your Google Cloud credentials.' },
        { status: 401 }
      );
    }

    if (error.code === 8) { // RESOURCE_EXHAUSTED
      return NextResponse.json(
        {
          error: 'Speech-to-Text quota exceeded. Please try again later.',
          note: 'Using v1 API with data logging for cost optimization'
        },
        { status: 429 }
      );
    }

    if (error.code === 3) { // INVALID_ARGUMENT
      return NextResponse.json(
        { error: 'Invalid audio file format or content.' },
        { status: 400 }
      );
    }

    // Generic error response
    return NextResponse.json(
      {
        error: 'Failed to process audio file. Please try again.',
        apiVersion: 'v1',
        dataLoggingEnabled: true
      },
      { status: 500 }
    );
  }
}

// Health check endpoint
export async function GET() {
  try {
    if (!speechClient) {
      return NextResponse.json(
        { status: 'unavailable', message: 'Speech-to-Text service not initialized' },
        { status: 503 }
      );
    }

    return NextResponse.json({
      status: 'available',
      message: 'Speech-to-Text service is working correctly',
      apiVersion: 'v1',
      dataLoggingEnabled: true,
      costOptimized: true,
      features: {
        transcriptionStorage: 'Firebase Storage (.txt files)',
        languageSupport: ['en-US', 'hi-IN'],
        autoLanguageDetection: true
      }
    });

  } catch (error) {
    console.error('Speech-to-Text health check failed:', error);
    return NextResponse.json(
      { 
        status: 'error', 
        message: 'Speech-to-Text service is not working properly',
        error: error.message 
      },
      { status: 503 }
    );
  }
}
