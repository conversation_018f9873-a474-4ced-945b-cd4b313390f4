import { NextResponse } from 'next/server';
import { initializeApp, getApps } from 'firebase/app';
import { getStorage, ref, listAll, getDownloadURL, getMetadata, deleteObject } from 'firebase/storage';

// Firebase configuration
const firebaseConfig = {
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID,
};

// Initialize Firebase
const app = getApps().length === 0 ? initializeApp(firebaseConfig) : getApps()[0];
const storage = getStorage(app);

// GET endpoint to list all stored transcriptions
export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit')) || 50;
    const sortBy = searchParams.get('sortBy') || 'date'; // date, name, size

    // List all files in the transcriptions folder
    const transcriptionsRef = ref(storage, 'transcriptions');
    const listResult = await listAll(transcriptionsRef);

    // Get metadata and download URLs for each file
    const transcriptions = await Promise.all(
      listResult.items.map(async (itemRef) => {
        try {
          const [metadata, downloadURL] = await Promise.all([
            getMetadata(itemRef),
            getDownloadURL(itemRef)
          ]);

          return {
            name: itemRef.name,
            fullPath: itemRef.fullPath,
            downloadURL: downloadURL,
            size: metadata.size,
            contentType: metadata.contentType,
            timeCreated: metadata.timeCreated,
            updated: metadata.updated,
            md5Hash: metadata.md5Hash,
          };
        } catch (error) {
          console.error(`Error getting metadata for ${itemRef.name}:`, error);
          return null;
        }
      })
    );

    // Filter out null results and sort
    const validTranscriptions = transcriptions.filter(t => t !== null);
    
    // Sort transcriptions
    validTranscriptions.sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.name.localeCompare(b.name);
        case 'size':
          return b.size - a.size;
        case 'date':
        default:
          return new Date(b.timeCreated) - new Date(a.timeCreated);
      }
    });

    // Apply limit
    const limitedTranscriptions = validTranscriptions.slice(0, limit);

    return NextResponse.json({
      success: true,
      transcriptions: limitedTranscriptions,
      total: validTranscriptions.length,
      limit: limit,
      sortBy: sortBy,
      metadata: {
        storageLocation: 'Firebase Storage /transcriptions/',
        apiVersion: 'v1',
        retrievedAt: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Error listing transcriptions:', error);
    return NextResponse.json(
      { 
        error: 'Failed to list transcriptions',
        details: error.message 
      },
      { status: 500 }
    );
  }
}

// DELETE endpoint to remove a specific transcription
export async function DELETE(request) {
  try {
    const { searchParams } = new URL(request.url);
    const filename = searchParams.get('filename');

    if (!filename) {
      return NextResponse.json(
        { error: 'Filename parameter is required' },
        { status: 400 }
      );
    }

    // Create reference to the file
    const fileRef = ref(storage, `transcriptions/${filename}`);
    
    // Delete the file
    await deleteObject(fileRef);

    return NextResponse.json({
      success: true,
      message: `Transcription ${filename} deleted successfully`,
      deletedAt: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error deleting transcription:', error);
    
    if (error.code === 'storage/object-not-found') {
      return NextResponse.json(
        { error: 'Transcription file not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(
      { 
        error: 'Failed to delete transcription',
        details: error.message 
      },
      { status: 500 }
    );
  }
}
