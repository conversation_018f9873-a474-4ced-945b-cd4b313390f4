import { NextResponse } from 'next/server';
import { getFirestore } from 'firebase-admin/firestore';
import { initializeApp as initializeAdminApp, cert, getApps as getAdminApps } from 'firebase-admin/app';

// Initialize Firebase Admin if not already initialized
if (!getAdminApps().length) {
  initializeAdminApp({
    credential: cert({
      projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
      clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
      privateKey: process.env.FIREBASE_PRIVATE_KEY.replace(/\\n/g, '\n'),
    }),
  });
}

const db = getFirestore();

// GET all images for a chat
export async function GET(request, { params }) {
  try {
    const { chatId } = await params;
    const { searchParams } = new URL(request.url);
    const nodeId = searchParams.get('nodeId');
    
    if (!chatId) {
      return NextResponse.json(
        { error: 'Missing chat ID' },
        { status: 400 }
      );
    }

    const imagesRef = db.collection('chats').doc(chatId).collection('images');
    let querySnapshot;

    if (nodeId) {
      querySnapshot = await imagesRef.where('nodeId', '==', nodeId).orderBy('createdAt', 'desc').get();
    } else {
      querySnapshot = await imagesRef.orderBy('createdAt', 'desc').get();
    }
    
    const images = [];
    querySnapshot.forEach((doc) => {
      images.push({
        id: doc.id,
        ...doc.data()
      });
    });

    return NextResponse.json({ images });
  } catch (error) {
    console.error('Error fetching images:', error);
    return NextResponse.json(
      { error: 'Failed to fetch images' },
      { status: 500 }
    );
  }
}

// POST create a new image
export async function POST(request, { params }) {
  try {
    const { chatId } = await params;
    const { url, prompt, nodeId } = await request.json();
    
    if (!chatId || !url) {
      return NextResponse.json(
        { error: 'Missing required parameters' },
        { status: 400 }
      );
    }

    const imageData = {
      url,
      prompt: prompt || '',
      nodeId: nodeId || '',
      createdAt: new Date()
    };

    const docRef = await db.collection('chats').doc(chatId).collection('images').add(imageData);
    
    return NextResponse.json({
      success: true,
      id: docRef.id,
      ...imageData
    });
  } catch (error) {
    console.error('Error creating image:', error);
    return NextResponse.json(
      { error: 'Failed to create image' },
      { status: 500 }
    );
  }
}