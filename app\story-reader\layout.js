import { Inter, Libre_Baskerville } from 'next/font/google';

const inter = Inter({ 
  subsets: ['latin'],
  variable: '--font-inter',
});

const libreBaskerville = Libre_Baskerville({ 
  subsets: ['latin'],
  weight: ['400', '700'],
  variable: '--font-libre-baskerville',
});

export const metadata = {
  title: 'Story Reader - The Money Tales',
  description: 'Read and listen to your interactive stories with text-to-speech functionality',
};

export default function StoryReaderLayout({ children }) {
  return (
    <div className={`${inter.variable} ${libreBaskerville.variable} bg-app-pattern min-h-screen`}>
      {children}
    </div>
  );
}
