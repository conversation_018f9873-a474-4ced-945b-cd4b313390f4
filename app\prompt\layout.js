import { libreBaskerville, figTree, slackey, inter } from '../style_vars';
import "../globals.css";

export const metadata = {
  title: "Create Audio Story - The Money Tales",
  description: "Create interactive audio stories with AI-generated alternate timelines.",
};

export default async function RootLayout({ children }) {
  return (
    <div className={`${libreBaskerville.variable} ${slackey.variable} ${figTree.variable} ${inter.variable} bg-app-pattern`}>
      {children}
    </div>
  );
}
