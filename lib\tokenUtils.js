export const generateToken = async (email) => {
    try {
      const text = `${email}-${process.env.UNSUBSCRIBE_SECRET}`;
      const encoder = new TextEncoder();
      const data = encoder.encode(text);
      const hashBuffer = await crypto.subtle.digest('SHA-256', data);
      const hashArray = Array.from(new Uint8Array(hashBuffer));
      return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
    } catch (error) {
      console.error('Error generating token:', error);
      throw error;
    }
  };