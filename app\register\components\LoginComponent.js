'use client';
import { signInWithEmailAndPassword, GoogleAuthProvider, signInWithPopup } from 'firebase/auth';
import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { auth } from '@/lib/firebase';
import Image from 'next/image';

export default function LoginComponent({ onBack }) {
  const [formData, setFormData] = useState({
    email: '',
    password: ''
  });
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const router = useRouter();

  useEffect(() => {
    auth.onAuthStateChanged(async (user) => {
      // setUser(user);
      console.log('Hello');
    });
  }, []);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    e.stopPropagation();
    setIsLoading(true);
    setError('');

    try {
      console.log('Login data:', formData);
      const credential = await signInWithEmailAndPassword(
        auth, 
        formData.email, 
        formData.password
      );
      const idToken = await credential.user.getIdToken();

      console.log('Login successful:', {
        uid: credential.user.uid,
        email: credential.user.email,
        displayName: credential.user.displayName
      });

      const res = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${idToken}`
        }
      });

      if (!res.ok) {
        const errorData = await res.text();
        throw new Error(`Session setup failed: ${errorData}`);
      }
      
      router.push('/dashboard');
      
    } catch (err) {
      setError(err.message || 'Login failed');
    } finally {
      setIsLoading(false);
    }
  };

  const handleGoogleLogin = async () => {
    setError('');
    setIsLoading(true);
    
    try {
      const provider = new GoogleAuthProvider();
      provider.setCustomParameters({
        prompt: 'select_account'
      });
      
      const result = await signInWithPopup(auth, provider);

      const idToken = await result.user.getIdToken();
      console.log('Login successful:', {
      uid: result.user.uid,
      email: result.user.email,
      displayName: result.user.displayName
    });
      
      // Call the login API to update last login and set cookies
      const res = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${idToken}`
        }
      });

      if (!res.ok) {
        const errorData = await res.text();
        throw new Error(`Session setup failed: ${errorData}`);
      }
      
      router.push('/dashboard');
    } catch (err) {
      setError(err.message || 'Google login failed');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (error) {
      const timer = setTimeout(() => setError(''), 5000);
      return () => clearTimeout(timer);
    }
  }, [error]);

  return (
    <div className="flex flex-col md:flex-row h-[100dvh] md:min-h-screen bg-app-pattern overflow-hidden">
      <div className="w-full md:w-1/2 p-4 md:p-6 flex flex-col justify-center relative">
        <button
          onClick={() => onBack('choice')}
          className="absolute top-4 left-4 md:top-6 md:left-6 text-app-text-secondary hover:text-app-text-primary transition-colors duration-200 z-20 text-sm md:text-base"
        >
          ← Back
        </button>

        <div className="max-w-sm mx-auto w-full space-y-4 mt-12 md:mt-0">
          <div className="space-y-1">
            <h1 className="text-2xl md:text-3xl font-bold text-app-text-primary font-inter">
              Welcome Back
            </h1>
            <p className="text-app-text-secondary text-sm font-inter">
              Sign in to your account to continue
            </p>
          </div>

          {error && (
            <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-3">
              <p className="text-red-400 text-sm">{error}</p>
            </div>
          )}

          <form onSubmit={handleSubmit} className="space-y-3">
            <div>
              <label className="block text-app-text-secondary text-sm mb-1 font-inter">
                Your email*
              </label>
              <input
                type="email"
                name="email"
                value={formData.email}
                onChange={handleInputChange}
                placeholder="Enter your email"
                required
                disabled={isLoading}
                className="w-full px-3 py-2.5 bg-app-surface-light border border-app-surface-lighter rounded-md text-app-text-primary placeholder-app-text-secondary focus:outline-none focus:border-app-text-secondary transition-colors duration-200 text-sm font-inter disabled:opacity-50"
              />
            </div>

            <div>
              <label className="block text-app-text-secondary text-sm mb-1 font-inter">
                Password*
              </label>
              <div className="relative">
                <input
                  type={showPassword ? 'text' : 'password'}
                  name="password"
                  value={formData.password}
                  onChange={handleInputChange}
                  placeholder="Enter password"
                  required
                  disabled={isLoading}
                  className="w-full px-3 py-2.5 bg-app-surface-light border border-app-surface-lighter rounded-md text-app-text-primary placeholder-app-text-secondary focus:outline-none focus:border-app-text-secondary transition-colors duration-200 pr-10 text-sm font-inter disabled:opacity-50"
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  disabled={isLoading}
                  className="absolute right-2.5 top-1/2 transform -translate-y-1/2 text-app-text-secondary hover:text-app-text-primary transition-colors duration-200 text-sm disabled:opacity-50"
                >
                  {showPassword ? '👁️' : '👁️‍🗨️'}
                </button>
              </div>
            </div>

            <div className="text-right">
              <button
                type="button"
                onClick={() => onBack('resetPassword')}
                disabled={isLoading}
                className="text-app-text-secondary hover:text-app-text-primary transition-colors duration-200 text-xs font-inter disabled:opacity-50"
              >
                Forgot Password?
              </button>
            </div>

            <button
              type="submit"
              disabled={isLoading}
              className="w-full py-2.5 px-4 bg-app-accent text-app-text-primary rounded-md font-medium hover:bg-app-accent-hover transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed text-sm font-inter"
            >
              {isLoading ? 'Signing in...' : 'Log in'}
            </button>
          </form>

          <div className="flex items-center space-x-3">
            <div className="flex-1 h-px bg-app-surface-lighter"></div>
            <span className="text-app-text-secondary text-xs font-inter">Or</span>
            <div className="flex-1 h-px bg-app-surface-lighter"></div>
          </div>

          <button
            type="button"
            onClick={handleGoogleLogin}
            disabled={isLoading}
            className="w-full py-2.5 px-4 bg-app-surface-light border border-app-surface-lighter text-app-text-primary rounded-md font-medium hover:bg-app-surface-lighter transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2 text-sm font-inter"
          >
            {isLoading ? (
              <span>Signing in...</span>
            ) : (
              <>
                <svg className="w-4 h-4" viewBox="0 0 24 24">
                  <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                  <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                  <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                  <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                </svg>
                <span>Login with Google</span>
              </>
            )}
          </button>

          <div className="text-center">
            <span className="text-app-text-secondary text-xs font-inter">New here? </span>
            <button
              type="button"
              onClick={() => onBack('signup')}
              disabled={isLoading}
              className="text-app-text-primary hover:text-app-text-secondary transition-colors duration-200 text-xs font-medium font-inter disabled:opacity-50"
            >
              Sign up
            </button>
          </div>
        </div>
      </div>

      <div className="hidden md:flex md:w-1/2 items-center justify-start p-4">
        <div className="relative w-full h-full max-h-[90vh] rounded-2xl overflow-hidden bg-gradient-to-br from-app-surface-light to-app-background shadow-2xl">

          <div className="absolute inset-0 hidden items-center justify-center">
            <div className="text-center space-y-4 p-8">
              <div className="w-32 h-32 mx-auto bg-app-text-secondary/20 rounded-full flex items-center justify-center">
                <svg className="w-16 h-16 text-app-text-secondary" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M21 19V5c0-1.1-.9-2-2-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2zM8.5 13.5l2.5 3.01L14.5 12l4.5 6H5l3.5-4.5z"/>
                </svg>
              </div>
              <p className="text-app-text-secondary text-sm">Classical painting</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
