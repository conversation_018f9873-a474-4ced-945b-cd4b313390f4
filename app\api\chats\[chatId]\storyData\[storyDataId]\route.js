import { NextResponse } from 'next/server';
import { 
  getDoc, 
  updateDoc, 
  deleteDoc, 
  doc,
  serverTimestamp 
} from 'firebase/firestore';
import { db } from '@/lib/firebase';

// GET a specific story data item
export async function GET(request, { params }) {
  try {
    const { chatId, storyDataId } = await params;
    
    if (!chatId || !storyDataId) {
      return NextResponse.json(
        { error: 'Missing required parameters' },
        { status: 400 }
      );
    }

    const storyDataDoc = await getDoc(doc(db, 'chats', chatId, 'storyData', storyDataId));
    
    if (!storyDataDoc.exists()) {
      return NextResponse.json(
        { error: 'Story data not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      id: storyDataDoc.id,
      ...storyDataDoc.data()
    });
  } catch (error) {
    console.error('Error fetching story data:', error);
    return NextResponse.json(
      { error: 'Failed to fetch story data' },
      { status: 500 }
    );
  }
}

// PUT update story data
export async function PUT(request, { params }) {
  try {
    const { chatId, storyDataId } = await params;
    const updateData = await request.json();
    
    if (!chatId || !storyDataId) {
      return NextResponse.json(
        { error: 'Missing required parameters' },
        { status: 400 }
      );
    }

    const storyDataRef = doc(db, 'chats', chatId, 'storyData', storyDataId);
    const storyDataDoc = await getDoc(storyDataRef);
    
    if (!storyDataDoc.exists()) {
      return NextResponse.json(
        { error: 'Story data not found' },
        { status: 404 }
      );
    }

    const dataWithTimestamp = {
      ...updateData,
      updatedAt: serverTimestamp()
    };

    await updateDoc(storyDataRef, dataWithTimestamp);
    
    return NextResponse.json({
      success: true,
      id: storyDataId
    });
  } catch (error) {
    console.error('Error updating story data:', error);
    return NextResponse.json(
      { error: 'Failed to update story data' },
      { status: 500 }
    );
  }
}

// DELETE story data
export async function DELETE(request, { params }) {
  try {
    const { chatId, storyDataId } = await params;
    
    if (!chatId || !storyDataId) {
      return NextResponse.json(
        { error: 'Missing required parameters' },
        { status: 400 }
      );
    }

    const storyDataRef = doc(db, 'chats', chatId, 'storyData', storyDataId);
    const storyDataDoc = await getDoc(storyDataRef);
    
    if (!storyDataDoc.exists()) {
      return NextResponse.json(
        { error: 'Story data not found' },
        { status: 404 }
      );
    }

    await deleteDoc(storyDataRef);
    
    return NextResponse.json({
      success: true,
      id: storyDataId
    });
  } catch (error) {
    console.error('Error deleting story data:', error);
    return NextResponse.json(
      { error: 'Failed to delete story data' },
      { status: 500 }
    );
  }
}