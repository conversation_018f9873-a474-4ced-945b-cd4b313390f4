import { NextResponse } from 'next/server';
import { getFirestore } from 'firebase-admin/firestore';
import { initializeApp as initializeAdminApp, cert, getApps as getAdminApps } from 'firebase-admin/app';

// Initialize Firebase Admin if not already initialized
if (!getAdminApps().length) {
  initializeAdminApp({
    credential: cert({
      projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
      clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
      privateKey: process.env.FIREBASE_PRIVATE_KEY.replace(/\\n/g, '\n'),
    }),
  });
}

const db = getFirestore();

// GET user data
export async function GET(_request, { params }) {
  try {
    const { userId } = await params;

    if (!userId) {
      return NextResponse.json(
        { error: 'Missing user ID' },
        { status: 400 }
      );
    }

    const userDoc = await db.collection('users').doc(userId).get();

    if (!userDoc.exists) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      id: userDoc.id,
      ...userDoc.data()
    });
  } catch (error) {
    console.error('Error fetching user:', error);
    return NextResponse.json(
      { error: 'Failed to fetch user' },
      { status: 500 }
    );
  }
}

// POST create or ensure user document exists
export async function POST(request, { params }) {
  try {
    const { userId } = await params;
    const body = await request.json();

    if (!userId) {
      return NextResponse.json(
        { error: 'Missing user ID' },
        { status: 400 }
      );
    }

    const userRef = db.collection('users').doc(userId);
    const userDoc = await userRef.get();

    if (!userDoc.exists) {
      // Create new user document
      const userData = {
        chats: [],
        createdAt: new Date(),
        updatedAt: new Date(),
        ...body // Allow additional user data to be passed
      };

      await userRef.set(userData);

      return NextResponse.json({
        success: true,
        id: userId,
        created: true,
        ...userData
      });
    } else {
      // User already exists, optionally update with new data
      if (Object.keys(body).length > 0) {
        await userRef.update({
          ...body,
          updatedAt: new Date()
        });
      }

      const updatedDoc = await userRef.get();
      return NextResponse.json({
        success: true,
        id: userId,
        created: false,
        ...updatedDoc.data()
      });
    }
  } catch (error) {
    console.error('Error creating/updating user:', error);
    return NextResponse.json(
      { error: 'Failed to create/update user' },
      { status: 500 }
    );
  }
}