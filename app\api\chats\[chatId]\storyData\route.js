import { NextResponse } from 'next/server';
import { adminDb } from '@/lib/firebase-admin';

// GET story data for a chat
export async function GET(request, { params }) {
  try {
    const { chatId } = await params;

    if (!chatId) {
      return NextResponse.json(
        { error: 'Missing chat ID' },
        { status: 400 }
      );
    }

    const storyDataDoc = await adminDb.collection('chats').doc(chatId).collection('storyData').doc('main').get();

    if (!storyDataDoc.exists) {
      return NextResponse.json(
        { error: 'Story data not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      id: storyDataDoc.id,
      ...storyDataDoc.data()
    });
  } catch (error) {
    console.error('Error fetching story data:', error);
    return NextResponse.json(
      { error: 'Failed to fetch story data' },
      { status: 500 }
    );
  }
}

// POST create or update story data
export async function POST(request, { params }) {
  try {
    const { chatId } = await params;
    const storyData = await request.json();

    if (!chatId) {
      return NextResponse.json(
        { error: 'Missing chat ID' },
        { status: 400 }
      );
    }

    const storyDataWithTimestamp = {
      ...storyData,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    await adminDb.collection('chats').doc(chatId).collection('storyData').doc('main').set(storyDataWithTimestamp);

    return NextResponse.json({
      success: true,
      id: 'main'
    });
  } catch (error) {
    console.error('Error saving story data:', error);
    return NextResponse.json(
      { error: 'Failed to save story data' },
      { status: 500 }
    );
  }
}
