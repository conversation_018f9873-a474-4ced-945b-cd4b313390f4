'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { auth, db } from '@/lib/firebase';
import { doc, getDoc } from 'firebase/firestore';
import { 
  ChevronRight, 
  User, 
  Globe, 
  Shield,
  ArrowLeft
} from 'lucide-react';

export default function ProfilePage() {
  const router = useRouter();
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [isSidebarOpen, setIsSidebarOpen] = useState(true);

  useEffect(() => {
    const fetchUserData = async () => {
      try {
        const currentUser = auth.currentUser;
        
        if (!currentUser) {
          setLoading(false);
          return;
        }
        
        // Fetch user profile from Firestore
        const userDoc = await getDoc(doc(db, 'users', currentUser.uid));
        
        if (userDoc.exists()) {
          setUser({
            ...userDoc.data(),
            email: currentUser.email || userDoc.data().email
          });
        } else {
          // If no Firestore document exists, use auth data
          setUser({
            uid: currentUser.uid,
            email: currentUser.email,
            firstName: currentUser.displayName?.split(' ')[0] || '',
            lastName: currentUser.displayName?.split(' ').slice(1).join(' ') || '',
            photoURL: currentUser.photoURL || ''
          });
        }
      } catch (err) {
        console.error('Error fetching user data:', err);
        setError('Failed to load profile data');
      } finally {
        setLoading(false);
      }
    };

    fetchUserData();
  }, []);

  if (loading) {
    return (
      <div className="flex h-screen bg-[#2a2d32] items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#8B5CF6]"></div>
      </div>
    );
  }

  return (
    <div className="flex h-screen bg-[#2a2d32] overflow-hidden">
      {/* Sidebar */}
      <aside className={`${isSidebarOpen ? 'w-64' : 'w-16'} bg-[#1e2023] transition-all duration-300 flex flex-col`}>
        {/* Logo/Brand */}
        <div className="p-4 border-b border-[#3a3d42]">
          <Link href="/" className="flex items-center space-x-2">
            {isSidebarOpen ? (
              <h1 className="font-slackey text-2xl text-white">What-if</h1>
            ) : (
              <span className="font-slackey text-xl text-white">W</span>
            )}
          </Link>
        </div>

        {/* Navigation */}
        <nav className="flex-1 p-4 space-y-2">
          {/* Dashboard */}
          <Link
            href="/dashboard"
            className="w-full flex items-center space-x-3 px-3 py-2 rounded-md text-[#696F79] hover:bg-[#3a3d42] hover:text-white transition-colors"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" 
                d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
            </svg>
            {isSidebarOpen && <span className="font-inter text-sm">Dashboard</span>}
          </Link>

          {/* My Stories */}
          <Link
            href="/dashboard"
            className="w-full flex items-center space-x-3 px-3 py-2 rounded-md text-[#696F79] hover:bg-[#3a3d42] hover:text-white transition-colors"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" 
                d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
            </svg>
            {isSidebarOpen && <span className="font-inter text-sm">My Stories</span>}
          </Link>

          {/* Profile - Active */}
          <div
            className="w-full flex items-center space-x-3 px-3 py-2 rounded-md bg-[#8B5CF6] text-white transition-colors"
          >
            <User className="w-5 h-5" />
            {isSidebarOpen && <span className="font-inter text-sm">Profile</span>}
          </div>

          {/* Settings */}
          <div className="w-full flex items-center space-x-3 px-3 py-2 rounded-md text-[#696F79] hover:bg-[#3a3d42] hover:text-white transition-colors">
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" 
                d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" 
                d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
            {isSidebarOpen && <span className="font-inter text-sm">Settings</span>}
          </div>
        </nav>

        {/* Logout Button */}
        <div className="p-4 border-t border-[#3a3d42]">
          <button
            onClick={() => router.push('/register')}
            className="w-full flex items-center space-x-3 px-3 py-2 rounded-md text-[#696F79] hover:bg-[#3a3d42] hover:text-white transition-colors"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" 
                d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
            </svg>
            {isSidebarOpen && <span className="font-inter text-sm">Logout</span>}
          </button>
        </div>

        {/* Toggle Sidebar */}
        <div className="p-4 border-t border-[#3a3d42]">
          <button
            onClick={() => setIsSidebarOpen(!isSidebarOpen)}
            className="w-full flex items-center justify-center p-2 rounded-md text-[#696F79] hover:bg-[#3a3d42] hover:text-white transition-colors"
          >
            <svg className={`w-5 h-5 transition-transform ${isSidebarOpen ? '' : 'rotate-180'}`} 
              fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" 
                d="M11 19l-7-7 7-7m8 14l-7-7 7-7" />
            </svg>
          </button>
        </div>
      </aside>

      {/* Main Content */}
      <main className="flex-1 flex flex-col overflow-hidden">
        {/* Header */}
        <header className="bg-[#1e2023] border-b border-[#3a3d42] px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Link href="/dashboard" className="flex items-center text-[#696F79] hover:text-white transition-colors">
                <ArrowLeft className="w-5 h-5 mr-2" />
                <span className="font-inter">Back to Dashboard</span>
              </Link>
              <h2 className="text-2xl font-bold text-white font-inter">Profile</h2>
            </div>
            <div className="flex items-center space-x-4">
              {/* User Avatar */}
              <div className="w-10 h-10 rounded-full bg-gradient-to-r from-[#8B5CF6] to-[#7C3AED] flex items-center justify-center">
                {user?.photoURL ? (
                  <Image
                    src={user.photoURL}
                    alt="Profile"
                    width={40}
                    height={40}
                    className="w-full h-full object-cover rounded-full"
                  />
                ) : (
                  <span className="text-white font-medium">
                    {user?.firstName?.charAt(0) || user?.email?.charAt(0) || 'U'}
                  </span>
                )}
              </div>
            </div>
          </div>
        </header>

        {/* Content Area */}
        <div className="flex-1 overflow-y-auto p-6">
          {/* Error Message */}
          {error && (
            <div className="mb-6 p-4 bg-red-500/10 border border-red-500/20 rounded-lg">
              <p className="text-red-400">Error: {error}</p>
              <button 
                onClick={() => setError(null)}
                className="text-sm text-red-300 hover:text-red-100 mt-2"
              >
                Dismiss
              </button>
            </div>
          )}

          <div className="max-w-4xl mx-auto">
            {/* Profile Card */}
            <div className="bg-[#1e2023] rounded-lg overflow-hidden mb-6">
              <div className="p-6 flex flex-col md:flex-row items-start md:items-center">
                <div className="w-24 h-24 rounded-full bg-gradient-to-r from-[#8B5CF6] to-[#7C3AED] flex items-center justify-center mb-4 md:mb-0 md:mr-6">
                  {user?.photoURL ? (
                    <Image
                      src={user.photoURL}
                      alt="Profile"
                      width={96}
                      height={96}
                      className="w-full h-full object-cover rounded-full"
                    />
                  ) : (
                    <span className="text-white text-3xl font-medium">
                      {user?.firstName?.charAt(0) || user?.email?.charAt(0) || 'U'}
                    </span>
                  )}
                </div>
                <div>
                  <h2 className="text-2xl font-bold text-white mb-1">
                    {user?.firstName} {user?.lastName}
                  </h2>
                  <p className="text-[#696F79] mb-3">{user?.email}</p>
                  {user?.role && (
                    <div className="inline-block px-3 py-1 bg-[#3a3d42] rounded-full text-sm text-white">
                      {user.role.charAt(0).toUpperCase() + user.role.slice(1)}
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* User Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Personal Information */}
              <div className="bg-[#1e2023] rounded-lg overflow-hidden">
                <div className="px-6 py-4 border-b border-[#3a3d42]">
                  <h3 className="text-lg font-semibold text-white">Personal Information</h3>
                </div>
                <div className="p-6 space-y-4">
                  <div>
                    <p className="text-sm text-[#696F79] mb-1">First Name</p>
                    <p className="text-white">{user?.firstName || 'Not provided'}</p>
                  </div>
                  <div>
                    <p className="text-sm text-[#696F79] mb-1">Last Name</p>
                    <p className="text-white">{user?.lastName || 'Not provided'}</p>
                  </div>
                  <div>
                    <p className="text-sm text-[#696F79] mb-1">Email Address</p>
                    <p className="text-white">{user?.email || 'Not provided'}</p>
                  </div>
                  <div>
                    <p className="text-sm text-[#696F79] mb-1">Phone Number</p>
                    <p className="text-white">{user?.phoneNumber || 'Not provided'}</p>
                  </div>
                </div>
              </div>

              {/* Account Information */}
              <div className="bg-[#1e2023] rounded-lg overflow-hidden">
                <div className="px-6 py-4 border-b border-[#3a3d42]">
                  <h3 className="text-lg font-semibold text-white">Account Information</h3>
                </div>
                <div className="p-6 space-y-4">
                  <div>
                    <p className="text-sm text-[#696F79] mb-1">User ID</p>
                    <p className="text-white text-sm font-mono overflow-hidden text-ellipsis">{user?.uid || 'Not available'}</p>
                  </div>
                  <div>
                    <p className="text-sm text-[#696F79] mb-1">Account Type</p>
                    <p className="text-white capitalize">{user?.provider || 'Email/Password'}</p>
                  </div>
                  {user?.createdAt && (
                    <div>
                      <p className="text-sm text-[#696F79] mb-1">Account Created</p>
                      <p className="text-white">
                        {user.createdAt.toDate ? 
                          new Date(user.createdAt.toDate()).toLocaleDateString('en-US', {
                            year: 'numeric',
                            month: 'long',
                            day: 'numeric'
                          }) : 'Not available'}
                      </p>
                    </div>
                  )}
                  {user?.lastLogin && (
                    <div>
                      <p className="text-sm text-[#696F79] mb-1">Last Login</p>
                      <p className="text-white">
                        {user.lastLogin.toDate ? 
                          new Date(user.lastLogin.toDate()).toLocaleDateString('en-US', {
                            year: 'numeric',
                            month: 'long',
                            day: 'numeric',
                            hour: '2-digit',
                            minute: '2-digit'
                          }) : 'Not available'}
                      </p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
