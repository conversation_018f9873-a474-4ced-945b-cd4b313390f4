
import { NextResponse } from 'next/server';
import { authConfig } from '@/lib/auth';
import { getTokens } from 'next-firebase-auth-edge';

const protectedRoutes = ['/dashboard', '/prompt', '/chat', '/alternate-scenario', '/onboarding'];
const authRoutes = ['/register']; // Add auth routes that should redirect to dashboard if logged in

export async function middleware(req) {
  const url = req.nextUrl.pathname;
  const tokens = await getTokens(req.cookies, {
    ...authConfig,
    serviceAccount: {
      projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
      clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
      privateKey: process.env.FIREBASE_PRIVATE_KEY.replace(/\\n/g, '\n'),
    }
  });

  // If trying to access protected routes without being logged in
  if (protectedRoutes.some((route) => url.startsWith(route))) {
    if (!tokens) {
      const signInUrl = new URL('/register', req.url);
      return NextResponse.redirect(signInUrl);
    }
  }

  // If trying to access auth routes while logged in
  if (authRoutes.some((route) => url.startsWith(route))) {
    if (tokens) {
      const dashboardUrl = new URL('/dashboard', req.url);
      return NextResponse.redirect(dashboardUrl);
    }
  }

  return NextResponse.next();
}
