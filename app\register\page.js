'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import SignupComponent from './components/SignupComponent';
import LoginComponent from './components/LoginComponent';
import ResetPasswordComponent from './components/ResetPasswordComponent';

export default function AuthPage() {
  const [view, setView] = useState('choice');

  const handleBack = (screen) => {
    setView(screen);
  };

  // Render full-screen login/signup/reset components when selected
  if (view === 'login') {
    return <LoginComponent onBack={handleBack} />;
  }

  if (view === 'signup') {
    return <SignupComponent onBack={handleBack} />;
  }
  
  if (view === 'resetPassword') {
    return <ResetPasswordComponent onBack={handleBack} />;
  }

  // Render choice view (default)
  return (
    <div className="flex flex-col md:flex-row h-[100dvh] md:min-h-screen bg-app-pattern overflow-hidden">
      {/* Right Panel - Choice View */}
      <div className="w-full md:w-1/2 p-3 md:p-8 flex items-center justify-center flex-grow">
        <div className="w-full max-w-md space-y-3 md:space-y-8">
          {/* New User Section */}
          <div className="text-center space-y-3 md:space-y-4 border-2 border-dotted border-[#696F79] p-4 md:p-8 rounded-lg bg-[#282A2F] md:bg-opacity-20 hover:bg-opacity-20 hover:bg-[#696F79] h-36 md:h-64 flex flex-col justify-center items-center transition-all duration-200">
            <h2 className="text-2xl md:text-3xl font-bold font-inter text-white">New here ?</h2>
            <div className="flex justify-center">
              <button
                onClick={() => setView('signup')}
                className="py-2 md:py-3 px-6 md:px-8 rounded-lg font-bold bg-[#696F79] text-white hover:bg-[#5a6069] transition-colors duration-200 min-w-[120px] md:min-w-[150px] text-base md:text-lg font-inter"
              >
                Sign up
              </button>
            </div>
          </div>

          {/* Existing User Section */}
          <div className="text-center space-y-3 md:space-y-4 border-2 border-dotted border-[#696F79] p-4 md:p-8 rounded-lg bg-[#282A2F] hover:bg-opacity-20 hover:bg-[#696F79] h-36 md:h-64 flex flex-col justify-center items-center transition-all duration-200">
            <h2 className="text-2xl md:text-3xl font-bold font-inter text-white">Got an Account already</h2>
            <div className="flex justify-center">
              <button
                onClick={() => setView('login')}
                className="py-2 md:py-3 px-6 md:px-8 rounded-lg font-bold bg-[#696F79] text-white hover:bg-[#5a6069] transition-colors duration-200 min-w-[120px] md:min-w-[150px] text-base md:text-lg font-inter"
              >
                Log in
              </button>
            </div>
          </div>
        </div>
      </div>
      {/* Left Panel - Desktop Only */}
      <div className="hidden md:flex md:w-1/2 p-8 flex-col justify-between">
        <a href="/" className="flex items-center space-x-2">
          <div className="w-32 h-12 font-slackey text-4xl text-white">
            MoneyTales
          </div>
        </a>

        <div className="space-y-6">
          <div className="flex justify-center">
            <div className="relative w-96 h-96">
              <Image
                src="/coin_mascot.svg"
                alt="Happy coin mascot"
                fill
                className="object-contain"
              />
            </div>
          </div>
          <div className="font-libre text-5xl font-bold text-white text-center mx-auto hidden lg:block">
            One Step closer to Financial Freedom
          </div>
        </div>
        <div className="h-8" />
      </div>

      {/* Mobile Top Section */}
      <div className="md:hidden">
        <div className="p-4 flex flex-col items-center pb-24 relative">
          <div className="font-slackey text-3xl text-white mb-4">
            MoneyTales
          </div>
          <div className="absolute top-14 w-44 h-44">
            <Image
              src="/coin_mascot_inverted.svg"
              alt="Happy coin mascot"
              fill
              className="object-contain"
            />
          </div>
        </div>
        <div className="font-inter text-2xl font-bold text-white max-w-64 mx-auto text-center mt-20 mb-2">
          One Step closer to Financial Freedom
        </div>
      </div>


    </div>
  );
}
