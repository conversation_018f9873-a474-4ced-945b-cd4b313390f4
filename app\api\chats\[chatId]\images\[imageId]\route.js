import { NextResponse } from 'next/server';
import { getFirestore } from 'firebase-admin/firestore';
import { initializeApp as initializeAdminApp, cert, getApps as getAdminApps } from 'firebase-admin/app';

// Initialize Firebase Admin if not already initialized
if (!getAdminApps().length) {
  initializeAdminApp({
    credential: cert({
      projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
      clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
      privateKey: process.env.FIREBASE_PRIVATE_KEY.replace(/\\n/g, '\n'),
    }),
  });
}

const db = getFirestore();

// GET a specific image
export async function GET(request, { params }) {
  try {
    const { chatId, imageId } = await params;
    
    if (!chatId || !imageId) {
      return NextResponse.json(
        { error: 'Missing required parameters' },
        { status: 400 }
      );
    }

    const imageDoc = await db.collection('chats').doc(chatId).collection('images').doc(imageId).get();

    if (!imageDoc.exists) {
      return NextResponse.json(
        { error: 'Image not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      id: imageDoc.id,
      ...imageDoc.data()
    });
  } catch (error) {
    console.error('Error fetching image:', error);
    return NextResponse.json(
      { error: 'Failed to fetch image' },
      { status: 500 }
    );
  }
}

// PUT update an image
export async function PUT(request, { params }) {
  try {
    const { chatId, imageId } = await params;
    const { url, prompt, nodeId } = await request.json();
    
    if (!chatId || !imageId) {
      return NextResponse.json(
        { error: 'Missing required parameters' },
        { status: 400 }
      );
    }

    const imageRef = db.collection('chats').doc(chatId).collection('images').doc(imageId);
    const imageDoc = await imageRef.get();

    if (!imageDoc.exists) {
      return NextResponse.json(
        { error: 'Image not found' },
        { status: 404 }
      );
    }

    const updateData = {};

    if (url !== undefined) updateData.url = url;
    if (prompt !== undefined) updateData.prompt = prompt;
    if (nodeId !== undefined) updateData.nodeId = nodeId;

    await imageRef.update(updateData);
    
    return NextResponse.json({
      success: true,
      id: imageId
    });
  } catch (error) {
    console.error('Error updating image:', error);
    return NextResponse.json(
      { error: 'Failed to update image' },
      { status: 500 }
    );
  }
}

// DELETE an image
export async function DELETE(request, { params }) {
  try {
    const { chatId, imageId } = await params;
    
    if (!chatId || !imageId) {
      return NextResponse.json(
        { error: 'Missing required parameters' },
        { status: 400 }
      );
    }

    const imageRef = db.collection('chats').doc(chatId).collection('images').doc(imageId);
    const imageDoc = await imageRef.get();

    if (!imageDoc.exists) {
      return NextResponse.json(
        { error: 'Image not found' },
        { status: 404 }
      );
    }

    await imageRef.delete();
    
    return NextResponse.json({
      success: true,
      id: imageId
    });
  } catch (error) {
    console.error('Error deleting image:', error);
    return NextResponse.json(
      { error: 'Failed to delete image' },
      { status: 500 }
    );
  }
}