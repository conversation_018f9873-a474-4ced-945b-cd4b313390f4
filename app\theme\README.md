# Centralized UI Theme System

This document explains the centralized theme system implemented for the What-if application.

## Overview

The centralized theme system provides consistent styling across all pages and components while maintaining the exact same visual appearance as before. The system includes:

1. **Centralized theme variables** in `app/style_vars.js`
2. **CSS custom properties** in `app/globals.css`
3. **Theme utilities and components** in `app/theme/index.js`
4. **React theme provider** in `app/theme/ThemeProvider.js`
5. **Tailwind configuration** updated to support theme classes

## Theme Structure

### Color System

The app uses a dark theme with the following color palette:

```javascript
colors: {
  app: {
    background: '#2a2d32',      // Main background
    surface: '#1e2023',         // Cards, sidebars, headers
    surfaceLight: '#3a3d42',    // Hover states, inputs
    surfaceLighter: '#4a4d52',  // Borders, dividers
    textPrimary: '#FFFFFF',     // Primary text
    textSecondary: '#696F79',   // Secondary text, labels
    accent: '#8B5CF6',          // Primary accent (purple)
    accentHover: '#7C3AED',     // Accent hover state
  }
}
```

### CSS Classes

The following CSS classes are available throughout the app:

#### Background Classes
- `bg-app-background` - Main app background (#2a2d32)
- `bg-app-surface` - Surface elements (#1e2023)
- `bg-app-surface-light` - Light surface elements (#3a3d42)
- `bg-app-surface-lighter` - Lighter surface elements (#4a4d52)

#### Text Classes
- `text-app-text-primary` - Primary text color (#FFFFFF)
- `text-app-text-secondary` - Secondary text color (#696F79)
- `text-app-accent` - Accent text color (#8B5CF6)

#### Accent Classes
- `bg-app-accent` - Accent background (#8B5CF6)
- `bg-app-accent-hover` - Accent hover background (#7C3AED)

#### Border Classes
- `border-app-surface-light` - Light surface borders
- `border-app-surface-lighter` - Lighter surface borders

#### Hover Classes
- `hover:bg-app-accent` - Hover accent background
- `hover:bg-app-accent-hover` - Hover accent hover background
- `hover:bg-app-surface-light` - Hover light surface background
- `hover:text-app-text-primary` - Hover primary text
- `hover:text-app-text-secondary` - Hover secondary text

## Usage Examples

### Basic Page Layout
```jsx
<div className="min-h-screen bg-app-background text-app-text-primary">
  {/* Page content */}
</div>
```

### Navigation Bar
```jsx
<nav className="bg-app-surface border-b border-app-surface-light">
  <div className="container mx-auto px-4 py-4">
    <h1 className="text-app-text-primary">Brand</h1>
    <a href="#" className="text-app-text-secondary hover:text-app-text-primary">
      Link
    </a>
  </div>
</nav>
```

### Button Components
```jsx
{/* Primary Button */}
<button className="px-4 py-2 bg-app-accent text-app-text-primary rounded-md hover:bg-app-accent-hover transition-colors">
  Primary Action
</button>

{/* Secondary Button */}
<button className="px-4 py-2 bg-transparent border border-app-accent text-app-accent hover:bg-app-accent hover:text-app-text-primary transition-colors">
  Secondary Action
</button>
```

### Card Components
```jsx
<div className="bg-app-surface rounded-lg p-6 border border-app-surface-light">
  <h3 className="text-app-text-primary font-semibold mb-2">Card Title</h3>
  <p className="text-app-text-secondary">Card content</p>
</div>
```

### Form Elements
```jsx
<div>
  <label className="block text-app-text-secondary text-sm mb-1">
    Label
  </label>
  <input 
    className="w-full px-3 py-2 bg-app-surface-light border border-app-surface-lighter rounded-md text-app-text-primary placeholder-app-text-secondary focus:outline-none focus:border-app-text-secondary transition-colors"
    placeholder="Enter text"
  />
</div>
```

## Theme Provider Components

The theme system includes pre-built React components for common UI elements:

### ThemedContainer
```jsx
<ThemedContainer variant="page">
  {/* Page content */}
</ThemedContainer>
```

### ThemedButton
```jsx
<ThemedButton variant="primary" size="md" onClick={handleClick}>
  Click me
</ThemedButton>
```

### ThemedInput
```jsx
<ThemedInput 
  label="Email"
  placeholder="Enter your email"
  error={errors.email}
/>
```

### ThemedText
```jsx
<ThemedText variant="h1">Heading</ThemedText>
<ThemedText variant="body">Body text</ThemedText>
<ThemedText variant="caption">Caption text</ThemedText>
```

## Migration Guide

### Before (Hardcoded Colors)
```jsx
<div className="bg-[#2a2d32] text-white">
  <nav className="bg-[#1e2023] border-b border-[#3a3d42]">
    <h1 className="text-white">Brand</h1>
    <a className="text-[#696F79] hover:text-white">Link</a>
  </nav>
</div>
```

### After (Theme Classes)
```jsx
<div className="bg-app-background text-app-text-primary">
  <nav className="bg-app-surface border-b border-app-surface-light">
    <h1 className="text-app-text-primary">Brand</h1>
    <a className="text-app-text-secondary hover:text-app-text-primary">Link</a>
  </nav>
</div>
```

## Benefits

1. **Consistency** - All pages use the same color values
2. **Maintainability** - Colors are defined in one place
3. **Flexibility** - Easy to change themes or add new variants
4. **Type Safety** - Theme values are exported from JavaScript
5. **Developer Experience** - Clear, semantic class names
6. **Future-Proof** - Easy to add dark/light mode switching

## Files Updated

The following files have been updated to use the centralized theme:

- `app/page.js` - Main landing page
- `app/dashboard/page.js` - Dashboard page
- `app/register/components/LoginComponent.js` - Login component
- `app/globals.css` - CSS custom properties and utility classes
- `app/style_vars.js` - Theme variables and configuration
- `tailwind.config.js` - Tailwind configuration for theme classes

## Next Steps

To complete the theme migration:

1. Update remaining auth components (SignupComponent, ResetPasswordComponent)
2. Update profile page and onboarding page
3. Update any remaining components in the app/components directory
4. Add theme switching functionality if needed
5. Consider adding theme variants (light mode, high contrast, etc.)
