/**
 * Centralized Theme Module
 * This module provides all theme-related utilities, styles, and configurations
 * used throughout the application to ensure consistency.
 */

import { colors, spacing, borderRadius, shadows, transitions, zIndex, typography, themes } from '../style_vars';

/**
 * Theme Utility Functions
 */
export const theme = {
  colors,
  spacing,
  borderRadius,
  shadows,
  transitions,
  zIndex,
  typography,
  themes,
  
  // Utility functions for getting theme values
  getColor: (path) => {
    const keys = path.split('.');
    let value = colors;
    for (const key of keys) {
      value = value[key];
      if (!value) return null;
    }
    return value;
  },
  
  getSpacing: (size) => spacing[size] || size,
  getBorderRadius: (size) => borderRadius[size] || size,
  getShadow: (size) => shadows[size] || size,
  getTransition: (speed) => transitions[speed] || speed,
  getZIndex: (layer) => zIndex[layer] || layer,
};

/**
 * Component Style Classes
 * These are CSS class names that can be used throughout the app
 */
export const componentStyles = {
  // Layout classes
  layout: {
    appBackground: 'min-h-screen bg-app-background',
    pageContainer: 'min-h-screen bg-app-pattern',
    flexCenter: 'flex items-center justify-center',
    flexBetween: 'flex items-center justify-between',
    container: 'container mx-auto px-4',
  },
  
  // Navigation classes
  nav: {
    base: 'bg-app-surface border-b border-app-surface-light sticky top-0 z-50',
    container: 'container mx-auto px-4 py-4',
    brand: 'font-slackey text-2xl md:text-3xl text-app-text-primary',
    link: 'text-app-text-secondary hover:text-app-text-primary transition-colors',
  },
  
  // Sidebar classes
  sidebar: {
    base: 'bg-app-surface transition-all duration-300 flex flex-col',
    expanded: 'w-64',
    collapsed: 'w-16',
    header: 'p-4 border-b border-app-surface-light',
    nav: 'flex-1 p-4 space-y-2',
    navItem: 'flex items-center space-x-3 px-3 py-2 rounded-lg text-app-text-secondary hover:bg-app-surface-light hover:text-app-text-primary transition-all duration-200',
    navItemActive: 'bg-app-accent text-app-text-primary',
  },
  
  // Card classes
  card: {
    base: 'bg-app-surface rounded-lg shadow-md',
    padding: 'p-6',
    border: 'border border-app-surface-light',
    hover: 'hover:shadow-lg transition-shadow duration-300',
  },
  
  // Button classes
  button: {
    base: 'px-4 py-2 rounded-md font-medium transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed',
    primary: 'bg-app-accent text-app-text-primary hover:bg-app-accent-hover',
    secondary: 'bg-transparent border border-app-accent text-app-accent hover:bg-app-accent hover:text-app-text-primary',
    ghost: 'bg-transparent text-app-text-secondary hover:bg-app-surface-light hover:text-app-text-primary',
    danger: 'bg-red-500 text-white hover:bg-red-600',
    sizes: {
      sm: 'px-3 py-1.5 text-sm',
      md: 'px-4 py-2',
      lg: 'px-6 py-3 text-lg',
    },
  },
  
  // Input classes
  input: {
    base: 'w-full px-3 py-2.5 bg-app-surface-light border border-app-surface-lighter rounded-md text-app-text-primary placeholder-app-text-secondary focus:outline-none focus:border-app-text-secondary transition-colors duration-200',
    label: 'block text-app-text-secondary text-sm mb-1 font-inter',
    error: 'border-red-500 focus:border-red-500',
    disabled: 'disabled:opacity-50',
  },
  
  // Text classes
  text: {
    primary: 'text-app-text-primary',
    secondary: 'text-app-text-secondary',
    accent: 'text-app-accent',
    error: 'text-red-400',
    success: 'text-green-400',
    warning: 'text-yellow-400',
    // Headings
    h1: 'text-2xl md:text-3xl font-bold text-app-text-primary font-inter',
    h2: 'text-xl md:text-2xl font-bold text-app-text-primary font-inter',
    h3: 'text-lg md:text-xl font-semibold text-app-text-primary font-inter',
    // Body text
    body: 'text-app-text-primary font-inter',
    bodySecondary: 'text-app-text-secondary font-inter',
    caption: 'text-sm text-app-text-secondary font-inter',
  },
  
  // Form classes
  form: {
    container: 'space-y-4',
    group: 'space-y-1',
    error: 'bg-red-500/10 border border-red-500/20 rounded-lg p-3',
    errorText: 'text-red-400 text-sm',
    success: 'bg-green-500/10 border border-green-500/20 rounded-lg p-3',
    successText: 'text-green-400 text-sm',
  },
  
  // Loading classes
  loading: {
    spinner: 'animate-spin rounded-full border-t-2 border-b-2 border-app-accent',
    overlay: 'fixed inset-0 bg-black/50 flex items-center justify-center z-50',
    container: 'flex items-center justify-center',
  },
  
  // Modal classes
  modal: {
    overlay: 'fixed inset-0 bg-black/50 flex items-center justify-center z-50',
    container: 'bg-app-surface rounded-lg shadow-xl max-w-md w-full mx-4',
    header: 'p-6 border-b border-app-surface-light',
    body: 'p-6',
    footer: 'p-6 border-t border-app-surface-light flex justify-end space-x-3',
  },
  
  // Utility classes
  utils: {
    srOnly: 'sr-only',
    hidden: 'hidden',
    block: 'block',
    flex: 'flex',
    inlineFlex: 'inline-flex',
    grid: 'grid',
    absolute: 'absolute',
    relative: 'relative',
    fixed: 'fixed',
    sticky: 'sticky',
    // Spacing
    p0: 'p-0', p1: 'p-1', p2: 'p-2', p3: 'p-3', p4: 'p-4', p6: 'p-6', p8: 'p-8',
    m0: 'm-0', m1: 'm-1', m2: 'm-2', m3: 'm-3', m4: 'm-4', m6: 'm-6', m8: 'm-8',
    // Flexbox
    itemsCenter: 'items-center',
    justifyCenter: 'justify-center',
    justifyBetween: 'justify-between',
    justifyEnd: 'justify-end',
    flexCol: 'flex-col',
    flexRow: 'flex-row',
    flexWrap: 'flex-wrap',
    flex1: 'flex-1',
    // Sizing
    wFull: 'w-full',
    hFull: 'h-full',
    wScreen: 'w-screen',
    hScreen: 'h-screen',
    minHScreen: 'min-h-screen',
    // Borders
    rounded: 'rounded',
    roundedMd: 'rounded-md',
    roundedLg: 'rounded-lg',
    roundedXl: 'rounded-xl',
    roundedFull: 'rounded-full',
    // Shadows
    shadowSm: 'shadow-sm',
    shadowMd: 'shadow-md',
    shadowLg: 'shadow-lg',
    shadowXl: 'shadow-xl',
    // Transitions
    transition: 'transition-all duration-300',
    transitionColors: 'transition-colors duration-200',
    transitionShadow: 'transition-shadow duration-300',
  },
};

/**
 * Responsive breakpoints
 */
export const breakpoints = {
  sm: '640px',
  md: '768px',
  lg: '1024px',
  xl: '1280px',
  '2xl': '1536px',
};

/**
 * Animation configurations
 */
export const animations = {
  fadeIn: 'fadeIn 0.3s ease-in-out',
  slideIn: 'slideIn 0.3s ease-out',
  scaleIn: 'scaleIn 0.2s ease-out',
  bounce: 'bounce 0.5s ease-in-out',
};

export default theme;
