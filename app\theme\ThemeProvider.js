'use client';

import React, { createContext, useContext } from 'react';
import { theme, componentStyles } from './index';

/**
 * Theme Context
 * Provides theme values and utilities throughout the app
 */
const ThemeContext = createContext({
  theme,
  componentStyles,
});

/**
 * Theme Provider Component
 * Wraps the app to provide theme context
 */
export function ThemeProvider({ children, className = '' }) {
  return (
    <ThemeContext.Provider value={{ theme, componentStyles }}>
      <div className={`${className}`}>
        {children}
      </div>
    </ThemeContext.Provider>
  );
}

/**
 * Hook to use theme context
 */
export function useTheme() {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
}

/**
 * Utility component for applying theme classes
 */
export function ThemedContainer({ 
  children, 
  variant = 'default',
  className = '',
  ...props 
}) {
  const { componentStyles } = useTheme();
  
  const getVariantClasses = () => {
    switch (variant) {
      case 'page':
        return 'min-h-screen bg-app-background';
      case 'pageWithPattern':
        return 'min-h-screen bg-app-pattern';
      case 'card':
        return 'bg-app-surface rounded-lg shadow-md p-6';
      case 'surface':
        return 'bg-app-surface';
      case 'modal':
        return 'bg-app-surface rounded-lg shadow-xl';
      default:
        return '';
    }
  };

  return (
    <div 
      className={`${getVariantClasses()} ${className}`}
      {...props}
    >
      {children}
    </div>
  );
}

/**
 * Themed Button Component
 */
export function ThemedButton({ 
  children, 
  variant = 'primary',
  size = 'md',
  className = '',
  disabled = false,
  ...props 
}) {
  const { componentStyles } = useTheme();
  
  const getVariantClasses = () => {
    switch (variant) {
      case 'primary':
        return 'bg-app-accent text-app-text-primary hover:bg-app-accent-hover';
      case 'secondary':
        return 'bg-transparent border border-app-accent text-app-accent hover:bg-app-accent hover:text-app-text-primary';
      case 'ghost':
        return 'bg-transparent text-app-text-secondary hover:bg-app-surface-light hover:text-app-text-primary';
      case 'danger':
        return 'bg-red-500 text-white hover:bg-red-600';
      default:
        return 'bg-app-accent text-app-text-primary hover:bg-app-accent-hover';
    }
  };

  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return 'px-3 py-1.5 text-sm';
      case 'lg':
        return 'px-6 py-3 text-lg';
      default:
        return 'px-4 py-2';
    }
  };

  return (
    <button
      className={`
        ${getVariantClasses()} 
        ${getSizeClasses()} 
        rounded-md font-medium transition-all duration-300 
        disabled:opacity-50 disabled:cursor-not-allowed
        ${className}
      `}
      disabled={disabled}
      {...props}
    >
      {children}
    </button>
  );
}

/**
 * Themed Input Component
 */
export function ThemedInput({ 
  label,
  error,
  className = '',
  ...props 
}) {
  const { componentStyles } = useTheme();

  return (
    <div className="space-y-1">
      {label && (
        <label className="block text-app-text-secondary text-sm mb-1 font-inter">
          {label}
        </label>
      )}
      <input
        className={`
          w-full px-3 py-2.5 bg-app-surface-light border border-app-surface-lighter 
          rounded-md text-app-text-primary placeholder-app-text-secondary 
          focus:outline-none focus:border-app-text-secondary transition-colors duration-200
          disabled:opacity-50
          ${error ? 'border-red-500 focus:border-red-500' : ''}
          ${className}
        `}
        {...props}
      />
      {error && (
        <p className="text-red-400 text-sm">{error}</p>
      )}
    </div>
  );
}

/**
 * Themed Text Component
 */
export function ThemedText({ 
  children,
  variant = 'body',
  className = '',
  ...props 
}) {
  const getVariantClasses = () => {
    switch (variant) {
      case 'h1':
        return 'text-2xl md:text-3xl font-bold text-app-text-primary font-inter';
      case 'h2':
        return 'text-xl md:text-2xl font-bold text-app-text-primary font-inter';
      case 'h3':
        return 'text-lg md:text-xl font-semibold text-app-text-primary font-inter';
      case 'body':
        return 'text-app-text-primary font-inter';
      case 'bodySecondary':
        return 'text-app-text-secondary font-inter';
      case 'caption':
        return 'text-sm text-app-text-secondary font-inter';
      case 'accent':
        return 'text-app-accent font-inter';
      case 'error':
        return 'text-red-400 font-inter';
      case 'success':
        return 'text-green-400 font-inter';
      default:
        return 'text-app-text-primary font-inter';
    }
  };

  return (
    <span 
      className={`${getVariantClasses()} ${className}`}
      {...props}
    >
      {children}
    </span>
  );
}

/**
 * Themed Card Component
 */
export function ThemedCard({ 
  children,
  className = '',
  hover = false,
  ...props 
}) {
  return (
    <div
      className={`
        bg-app-surface rounded-lg shadow-md p-6 border border-app-surface-light
        ${hover ? 'hover:shadow-lg transition-shadow duration-300' : ''}
        ${className}
      `}
      {...props}
    >
      {children}
    </div>
  );
}

/**
 * Themed Loading Spinner
 */
export function ThemedSpinner({ 
  size = 'md',
  className = '',
  ...props 
}) {
  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return 'h-4 w-4';
      case 'lg':
        return 'h-8 w-8';
      case 'xl':
        return 'h-12 w-12';
      default:
        return 'h-6 w-6';
    }
  };

  return (
    <div
      className={`
        animate-spin rounded-full border-t-2 border-b-2 border-app-accent
        ${getSizeClasses()}
        ${className}
      `}
      {...props}
    />
  );
}

export default ThemeProvider;
