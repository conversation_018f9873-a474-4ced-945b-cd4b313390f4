# The Money Tales

A Next.js application for creating interactive audio stories with AI-generated alternate timeline fiction scenarios.

## Features

- Interactive audio story creation
- AI-powered alternate timeline generation using Google's Gemini AI
- High-quality text-to-speech with multi-language support (Hindi & English)
- Story reader with audio navigation and playback controls

## Getting Started

### Prerequisites

- Node.js 18.x or higher
- A Google API key for Gemini AI
- Google Cloud Text-to-Speech API enabled
- Firebase project for authentication and data storage

### Environment Setup

1. Copy the `.env.local.example` file to `.env.local`:

```bash
cp .env.local.example .env.local
```

2. Fill in the required environment variables:
   - For Firebase configuration (required for authentication and data storage)
   - For Google Gemini API (required for story generation)
   - For Google Cloud Text-to-Speech (required for audio functionality)

### Installation

Install the dependencies:

```bash
npm install
# or
yarn install
# or
pnpm install
```

### Running the Development Server

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the application.

## How It Works

### Audio Story Creation

1. Enter book details (title, author, setting)
2. Provide a "what-if" scenario to explore
3. AI generates an interactive story with multiple paths and choices
4. Listen to the story with high-quality text-to-speech

### Story Experience

1. After analyzing a PDF, click on "Create Alternate Scenario"
2. Fill in the details about the book and the "what if" scenario
3. The application uses Google's Gemini AI to generate an alternate timeline fiction scenario
4. The system automatically generates visual images using Freepik AI based on the scenario descriptions
5. The generated scenario includes both text content and AI-generated images for immersive storytelling

## Technologies Used

- [Next.js](https://nextjs.org/) - React framework
- [Google Generative AI (Gemini)](https://ai.google.dev/) - Story generation
- [Google Cloud Text-to-Speech](https://cloud.google.com/text-to-speech) - High-quality audio synthesis
- [Firebase](https://firebase.google.com/) - Authentication and data storage
- [Lucide React](https://lucide.dev/) - Beautiful icons

## License

This project is licensed under the MIT License - see the LICENSE file for details.
