# Hindi Language Support Guide

## Overview

The Story Reader now supports Hindi language content with automatic language detection and appropriate Hindi text-to-speech voices. This guide explains how to ensure proper Hindi support throughout the application.

## 🎯 Key Features

### 1. **Automatic Language Detection**
- Detects Hindi content using Devanagari script Unicode ranges (U+0900-U+097F)
- Automatically switches to Hindi TTS voices when Hindi content is detected
- Supports mixed content with intelligent language detection

### 2. **Hindi Text-to-Speech Voices**
- **Female Voice**: `hi-IN-Wavenet-A` (<PERSON><PERSON>ult)
- **Male Voice**: `hi-IN-Wavenet-B`
- **Additional Options**: `hi-IN-Wavenet-C` (Female), `hi-IN-Wavenet-D` (Male)
- Optimized speaking rate for Hindi content (0.9x speed)

### 3. **Voice Selection Interface**
- User-friendly voice selection for Hindi content
- Bilingual labels: हिंदी/English
- Real-time voice switching without page reload

## 🔧 Configuration for Hindi Content Generation

### Gemini AI Configuration

To ensure Gemini generates content in Hindi when given Hindi input, you need to modify the prompts in your story generation APIs. Here are the key files to update:

#### 1. **Story Tree Generation** (`app/api/generate-story-tree/route.js`)

Add language-specific instructions to the prompt:

```javascript
// Add this language detection and instruction
const isHindiInput = /[\u0900-\u097F]/.test(bookTitle + author + whatIfPrompt);
const languageInstruction = isHindiInput 
  ? `\n\nIMPORTANT: The input appears to be in Hindi. Please generate ALL content in Hindi language only. Use proper Devanagari script throughout the response. Do not mix English words unless they are commonly used Hindi terms.`
  : `\n\nIMPORTANT: Generate content in English language.`;

// Add to your existing prompt
const prompt = `Your existing prompt here...${languageInstruction}`;
```

#### 2. **Alternate Scenario Generation** (`app/api/alternate-scenario-game-data/route.js`)

Similar language detection and instruction:

```javascript
const isHindiContent = /[\u0900-\u097F]/.test(userPrompt + bookInfo.title);
const languageInstruction = isHindiContent
  ? `\n\nभाषा निर्देश: सभी कंटेंट केवल हिंदी में जेनरेट करें। देवनागरी लिपि का उपयोग करें।`
  : `\n\nLanguage: Generate content in English.`;
```

### Example Hindi Prompts

#### For Story Generation:
```
आपको एक हिंदी पुस्तक के लिए वैकल्पिक कहानी बनानी है।

पुस्तक: [Book Title in Hindi]
लेखक: [Author Name]
परिस्थिति: [What-if scenario in Hindi]

कृपया निम्नलिखित JSON फॉर्मेट में उत्तर दें:
{
  "rootScenario": {
    "title": "कहानी का शीर्षक",
    "text": "कहानी का विस्तृत विवरण...",
    "choices": [...]
  }
}
```

## 🎨 UI Updates for Hindi Support

### Voice Settings Component

The voice settings automatically appear when Hindi content is detected:

```jsx
{/* Voice Settings for Hindi Content */}
{isHindiContent && (
  <div className="mb-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
    <div className="flex items-center gap-2">
      <Volume2 className="w-4 h-4 text-blue-600" />
      <span className="text-sm font-medium text-blue-800">
        हिंदी आवाज़ सेटिंग्स (Hindi Voice Settings)
      </span>
    </div>
    {/* Voice selection buttons */}
  </div>
)}
```

### Bilingual Labels

All Hindi-related UI elements include both Hindi and English labels:
- `महिला (Female)` / `पुरुष (Male)`
- `हिंदी आवाज़ सेटिंग्स (Hindi Voice Settings)`
- `कहानी के विकल्प (Story choices)`

## 🔍 Language Detection Logic

### Text Analysis
```javascript
function detectLanguage(text) {
  const hindiRegex = /[\u0900-\u097F]/;
  const hindiMatches = text.match(hindiRegex);
  const hindiCharCount = hindiMatches ? hindiMatches.length : 0;
  
  const englishRegex = /[a-zA-Z]/g;
  const englishMatches = text.match(englishRegex);
  const englishCharCount = englishMatches ? englishMatches.length : 0;
  
  const totalChars = hindiCharCount + englishCharCount;
  // If more than 30% Hindi characters, treat as Hindi
  return (totalChars > 0 && (hindiCharCount / totalChars) > 0.3) ? 'hi-IN' : 'en-US';
}
```

### Voice Configuration
```javascript
function getVoiceConfig(languageCode, preferredGender = 'FEMALE') {
  const voiceConfigs = {
    'hi-IN': {
      languageCode: 'hi-IN',
      name: preferredGender === 'MALE' ? 'hi-IN-Wavenet-B' : 'hi-IN-Wavenet-A',
      ssmlGender: preferredGender,
    },
    'en-US': {
      languageCode: 'en-US',
      name: 'en-US-Journey-D',
      ssmlGender: 'NEUTRAL',
    }
  };
  
  return voiceConfigs[languageCode] || voiceConfigs['en-US'];
}
```

## 🧪 Testing Hindi Support

### Test Cases

1. **Pure Hindi Content**
   ```
   Title: "रामायण"
   Content: "यह एक प्राचीन भारतीय महाकाव्य है..."
   Expected: Hindi voice, Hindi UI elements
   ```

2. **Mixed Content**
   ```
   Title: "Modern Ramayana"
   Content: "राम और सीता की कहानी..."
   Expected: Hindi voice (>30% Hindi chars)
   ```

3. **English Content**
   ```
   Title: "The Great Gatsby"
   Content: "In the summer of 1922..."
   Expected: English voice, no Hindi UI
   ```

### Manual Testing Steps

1. Upload a Hindi PDF or enter Hindi book details
2. Generate story content
3. Navigate to Story Reader
4. Verify:
   - Hindi voice settings appear
   - Correct voice is used for TTS
   - UI shows bilingual labels
   - Content is properly displayed in Devanagari

## 🔧 Troubleshooting

### Common Issues

1. **English TTS for Hindi Content**
   - Check language detection logic
   - Verify Unicode ranges in regex
   - Test with sample Hindi text

2. **Voice Not Available**
   - Ensure Google Cloud TTS API has Hindi voices enabled
   - Check service account permissions
   - Verify voice names in Google Cloud Console

3. **Mixed Language Output from Gemini**
   - Add explicit language instructions to prompts
   - Use language detection in API routes
   - Test with clear Hindi input examples

### Debug Commands

```javascript
// Test language detection
console.log(detectLanguage("यह हिंदी टेक्स्ट है")); // Should return 'hi-IN'
console.log(detectLanguage("This is English text")); // Should return 'en-US'

// Test voice configuration
console.log(getVoiceConfig('hi-IN', 'FEMALE')); // Should return Hindi female voice
console.log(getVoiceConfig('hi-IN', 'MALE'));   // Should return Hindi male voice
```

## 📝 Best Practices

### For Content Generation
1. Always include explicit language instructions in Gemini prompts
2. Detect input language before generating content
3. Use consistent terminology across Hindi content
4. Test with various Hindi input scenarios

### For TTS Implementation
1. Cache audio files to improve performance
2. Provide fallback to English voice if Hindi voice fails
3. Allow users to switch voices mid-session
4. Monitor TTS API usage and costs

### For UI/UX
1. Use bilingual labels for clarity
2. Make voice settings easily accessible
3. Provide visual feedback for language detection
4. Ensure proper font support for Devanagari script

## 🚀 Future Enhancements

### Planned Features
- Support for other Indian languages (Tamil, Telugu, Bengali)
- Regional Hindi accent variations
- Custom voice training for specific content types
- Automatic script transliteration
- Enhanced language mixing detection

### Technical Improvements
- Improved language detection algorithms
- Better caching strategies for multilingual content
- Performance optimization for Hindi TTS
- Advanced voice customization options
