'use client';
import { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { auth, db, storage } from '@/lib/firebase';
import { ref, uploadBytes, getDownloadURL } from 'firebase/storage';
import { doc, setDoc, serverTimestamp } from 'firebase/firestore';
import Image from 'next/image';
import PhoneInput from 'react-phone-number-input';
import 'react-phone-number-input/style.css';

export default function OnboardingPage () {
    const router = useRouter();
    const [user, setUser] = useState(null);
    const [loading, setLoading] = useState(true);
    const [submitting, setSubmitting] = useState(false);
    const [error, setError] = useState('');
    const [profileImage, setProfileImage] = useState(null);
    const [profileImageUrl, setProfileImageUrl] = useState('');
    const fileInputRef = useRef(null);

    const [formData, setFormData] = useState({
        firstName: '',
        lastName: '',
        phoneNumber: '',
        role: 'individual', // Default value
    });

    useEffect(() => {
        const unsubscribe = auth.onAuthStateChanged((currentUser) => {
        if (currentUser) {
            setUser(currentUser);
            if (currentUser.displayName) {
            const nameParts = currentUser.displayName.split(' ');
            setFormData(prev => ({
                ...prev,
                firstName: nameParts[0] || '',
                lastName: nameParts.slice(1).join(' ') || ''
            }));
            }
            setProfileImageUrl(currentUser.photoURL || '');
        } else {
            // Redirect to login if not authenticated
            router.push('/register');
        }
        setLoading(false);
        });

        return () => unsubscribe();
    }, [router]);

    const handleInputChange = (e) => {
        const { name, value } = e.target;
        setFormData(prev => ({
        ...prev,
        [name]: value
        }));
    };
    
    const handlePhoneChange = (value) => {
    setFormData(prev => ({
        ...prev,
        phoneNumber: value || ''
    }));
    };

    const handleImageClick = () => {
    fileInputRef.current.click();
    };

    const handleImageChange = (e) => {
        if (e.target.files && e.target.files[0]) {
        const file = e.target.files[0];
        setProfileImage(file);
        setProfileImageUrl(URL.createObjectURL(file));
        }
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        if (!formData.firstName) {
            setError('First name is required');
            return;
        }

        setSubmitting(true);
        setError('');

        try {
            const uid = user.uid;
            const email = user.email;
            const provider = user.providerData[0]?.providerId || 'password';
            
            // Upload profile image to Firebase Storage if it exists
            let photoURL = profileImageUrl || user.photoURL || '';
            
            if (profileImage) {
                try {
                    // Create a storage reference
                    const storageRef = ref(storage, `profile_images/${uid}`);
                    
                    // Upload the file with CORS metadata
                    const metadata = {
                        contentType: profileImage.type,
                        customMetadata: {
                            'Access-Control-Allow-Origin': '*'
                        }
                    };
                    
                    // Upload the file
                    await uploadBytes(storageRef, profileImage, metadata);
                    
                    // Get the download URL
                    photoURL = await getDownloadURL(storageRef);
                } catch (uploadError) {
                    console.error("Image upload error:", uploadError);
                    // Continue with the rest of the profile update even if image upload fails
                }
            }

            await setDoc(doc(db, 'users', uid), {
                uid,
                email,
                firstName: formData.firstName,
                lastName: formData.lastName || '',
                phoneNumber: formData.phoneNumber || '',
                role: formData.role,
                photoURL: photoURL,
                provider,
                createdAt: serverTimestamp(),
                lastLogin: serverTimestamp(),
                isBlocked: false,
                isDeleted: false
            });

            router.push('/dashboard');
        } catch (err) {
            setError(err.message || 'Failed to complete onboarding');
        } finally {
            setSubmitting(false);
        }
    };

    if (loading) {
        return (
            <div className="flex items-center justify-center min-h-screen bg-app-pattern">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#8B5CF6]"></div>
            </div>
        );
    }

    return (
        <div className="min-h-screen bg-app-pattern flex items-center justify-center p-4">
            <div className="bg-[#282A2F] rounded-xl shadow-xl max-w-md w-full p-6 md:p-8">
                <h1 className="text-2xl font-bold text-white mb-6 text-center">Complete Your Profile</h1>
                
                {error && (
                <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-3 mb-4">
                    <p className="text-red-400 text-sm">{error}</p>
                </div>
                )}

                <form onSubmit={handleSubmit} className="space-y-5">
                    {/* Profile Image */}
                    <div className="flex flex-col items-center mb-6">
                        <div 
                        onClick={handleImageClick}
                        className="relative w-24 h-24 rounded-full overflow-hidden bg-[#3a3d42] cursor-pointer hover:opacity-90 transition-opacity border-2 border-[#4a4d52] flex items-center justify-center"
                        >
                        {profileImageUrl ? (
                            <Image 
                            src={profileImageUrl} 
                            alt="Profile" 
                            fill 
                            className="object-cover"
                            />
                        ) : (
                            <svg className="w-12 h-12 text-[#696F79]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                            </svg>
                        )}
                        </div>
                        <input 
                        type="file" 
                        ref={fileInputRef} 
                        onChange={handleImageChange} 
                        accept="image/*" 
                        className="hidden" 
                        />
                        <p className="text-[#696F79] text-sm mt-2">Profile Photo (optional)</p>
                    </div>

                    {/* First Name */}
                    <div>
                        <label className="block text-[#696F79] text-sm mb-1 font-inter">
                        First Name*
                        </label>
                        <input
                        type="text"
                        name="firstName"
                        value={formData.firstName}
                        onChange={handleInputChange}
                        placeholder="Enter your first name"
                        required
                        className="w-full px-3 py-2.5 bg-[#3a3d42] border border-[#4a4d52] rounded-md text-white placeholder-[#696F79] focus:outline-none focus:border-[#696F79] transition-colors duration-200 text-sm font-inter"
                        />
                    </div>

                    {/* Last Name */}
                    <div>
                        <label className="block text-[#696F79] text-sm mb-1 font-inter">
                        Last Name (optional)
                        </label>
                        <input
                        type="text"
                        name="lastName"
                        value={formData.lastName}
                        onChange={handleInputChange}
                        placeholder="Enter your last name"
                        className="w-full px-3 py-2.5 bg-[#3a3d42] border border-[#4a4d52] rounded-md text-white placeholder-[#696F79] focus:outline-none focus:border-[#696F79] transition-colors duration-200 text-sm font-inter"
                        />
                    </div>

                    {/* Phone Number */}
                    <div>
                        <label className="block text-[#696F79] text-sm mb-1 font-inter">
                        Phone Number
                        </label>
                        <PhoneInput
                        international
                        defaultCountry="US"
                        value={formData.phoneNumber}
                        onChange={handlePhoneChange}
                        className="w-full px-3 py-2.5 bg-[#3a3d42] border border-[#4a4d52] rounded-md text-white placeholder-[#696F79] focus:outline-none focus:border-[#696F79] transition-colors duration-200 text-sm font-inter"
                        />
                    </div>

                    {/* Role Selection */}
                    <div>
                        <label className="block text-[#696F79] text-sm mb-1 font-inter">
                        I am a(n)
                        </label>
                        <div className="grid grid-cols-2 gap-3">
                            <button
                                type="button"
                                onClick={() => setFormData(prev => ({ ...prev, role: 'individual' }))}
                                className={`py-2.5 px-4 rounded-md text-sm font-medium transition-colors duration-200 ${
                                formData.role === 'individual' 
                                    ? 'bg-[#8B5CF6] text-white' 
                                    : 'bg-[#3a3d42] text-[#696F79] hover:bg-[#4a4d52]'
                                }`}
                            >
                                Individual
                            </button>
                            <button
                                type="button"
                                onClick={() => setFormData(prev => ({ ...prev, role: 'organization' }))}
                                className={`py-2.5 px-4 rounded-md text-sm font-medium transition-colors duration-200 ${
                                formData.role === 'organization' 
                                    ? 'bg-[#8B5CF6] text-white' 
                                    : 'bg-[#3a3d42] text-[#696F79] hover:bg-[#4a4d52]'
                                }`}
                            >
                                Organization
                            </button>
                        </div>
                    </div>

                    {/* Submit Button */}
                    <button
                        type="submit"
                        disabled={submitting}
                        className="w-full py-2.5 px-4 bg-[#8B5CF6] text-white rounded-md font-medium hover:bg-[#7C3AED] transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed text-sm font-inter mt-6"
                    >
                        {submitting ? 'Saving...' : 'Complete Profile'}
                    </button>
                </form>
            </div>
        </div>
    );
    
}
