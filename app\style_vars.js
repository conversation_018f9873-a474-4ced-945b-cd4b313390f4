import localFont from 'next/font/local';

/**
 * Font Definitions
 * These are the custom fonts used throughout the application
 */
const libreBaskerville = localFont({
  src: [
    {
      path: 'fonts/LibreBaskerville-Regular.ttf',
      weight: '400',
      style: 'normal',
    },
    {
      path: 'fonts/LibreBaskerville-Regular.ttf',
      weight: '700',
      style: 'normal',
    },
  ],
  variable: '--font-libre-baskerville',
  display: 'swap',
});

const slackey = localFont({
  src: [
    {
      path: 'fonts/Slackey-Regular.ttf',
      weight: '400',
      style: 'normal',
    },
  ],
  variable: '--font-slackey',
  display: 'swap',
});

const figTree = localFont({
  src: [
    {
      path: 'fonts/Figtree-VariableFont_wght.ttf',
      weight: '400',
      style: 'normal',
    },
    {
      path: 'fonts/Figtree-VariableFont_wght.ttf',
      weight: '500',
      style: 'normal',
    },
    {
      path: 'fonts/Figtree-VariableFont_wght.ttf',
      weight: '600',
      style: 'normal',
    },
    {
      path: 'fonts/Figtree-VariableFont_wght.ttf',
      weight: '700',
      style: 'normal',
    },
    {
      path: 'fonts/Figtree-VariableFont_wght.ttf',
      weight: '800',
      style: 'normal',
    },
    {
      path: 'fonts/Figtree-VariableFont_wght.ttf',
      weight: '900',
      style: 'normal',
    },
  ],
  variable: '--font-figtree',
  display: 'swap',
});

const inter = localFont({
  src: [
    {
      path: 'fonts/Inter/Inter-VariableFont_opsz,wght.ttf',
      weight: '100 900',
      style: 'normal',
    },
    {
      path: 'fonts/Inter/Inter-Italic-VariableFont_opsz,wght.ttf',
      weight: '100 900',
      style: 'italic',
    },
  ],
  variable: '--font-inter',
  display: 'swap',
});

/**
 * Color System
 * These are the main colors used throughout the application
 */
export const colors = {
  primary: {
    green: '#13824B',
    greenLight: '#1A9D5C',
    greenDark: '#0F6B3D',
  },
  secondary: {
    green: '#13824B',
  },
  neutral: {
    cream: '#FFF4E0',
    creamLight: '#FFF9ED',
    creamDark: '#F5E8D0',
    black: '#1A1A1A',
    white: '#FFFFFF',
  },
  gray: {
    100: '#F5F5F5',
    200: '#E5E5E5',
    300: '#D4D4D4',
    400: '#A3A3A3',
    500: '#737373',
    600: '#525252',
    700: '#404040',
    800: '#262626',
    900: '#171717',
  },
  feedback: {
    success: '#22C55E',
    warning: '#F59E0B',
    error: '#EF4444',
    info: '#3B82F6',
  },
  // App-specific dark theme colors
  app: {
    background: '#2a2d32',
    surface: '#1e2023',
    surfaceLight: '#3a3d42',
    surfaceLighter: '#4a4d52',
    textPrimary: '#FFFFFF',
    textSecondary: '#696F79',
    accent: '#8B5CF6',
    accentHover: '#7C3AED',
    purple: {
      primary: '#8B5CF6',
      hover: '#7C3AED',
      gradient: {
        from: '#7C3AED',
        to: '#6B21A8'
      }
    }
  },
};

/**
 * Spacing System
 * Consistent spacing values used throughout the application
 */
export const spacing = {
  xs: '0.25rem',  // 4px
  sm: '0.5rem',   // 8px
  md: '1rem',     // 16px
  lg: '1.5rem',   // 24px
  xl: '2rem',     // 32px
  '2xl': '3rem',  // 48px
  '3xl': '4rem',  // 64px
};

/**
 * Border Radius
 * Consistent border radius values
 */
export const borderRadius = {
  sm: '0.25rem', // 4px
  md: '0.5rem',  // 8px
  lg: '1rem',    // 16px
  full: '9999px',
};

/**
 * Shadows
 * Consistent shadow values
 */
export const shadows = {
  sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
  md: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
  lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
};

/**
 * Transitions
 * Consistent transition values
 */
export const transitions = {
  fast: '150ms ease',
  normal: '300ms ease',
  slow: '500ms ease',
};

/**
 * Z-index layers
 * Consistent z-index values to manage stacking context
 */
export const zIndex = {
  base: 1,
  dropdown: 10,
  sticky: 100,
  fixed: 1000,
  modal: 1001,
  popover: 1010,
  tooltip: 1100,
};

/**
 * Theme Configuration
 * Defines the light and dark theme variables
 */
export const themes = {
  light: {
    background: colors.neutral.cream,
    foreground: colors.neutral.black,
    textPrimary: colors.primary.green,
    textSecondary: colors.gray[700],
    cardBg: colors.neutral.white,
    cardBorder: colors.gray[200],
    inputBg: colors.neutral.white,
    inputBorder: colors.gray[300],
    inputText: colors.neutral.black,
    buttonPrimaryBg: colors.primary.green,
    buttonPrimaryText: colors.neutral.white,
  },
  dark: {
    background: colors.gray[900],
    foreground: colors.neutral.cream,
    textPrimary: colors.primary.greenLight,
    textSecondary: colors.gray[300],
    cardBg: colors.gray[800],
    cardBorder: colors.gray[700],
    inputBg: colors.gray[800],
    inputBorder: colors.gray[600],
    inputText: colors.neutral.white,
    buttonPrimaryBg: colors.primary.greenLight,
    buttonPrimaryText: colors.neutral.white,
  },
  // App theme - the current dark theme used throughout the app
  app: {
    background: colors.app.background,
    surface: colors.app.surface,
    surfaceLight: colors.app.surfaceLight,
    surfaceLighter: colors.app.surfaceLighter,
    textPrimary: colors.app.textPrimary,
    textSecondary: colors.app.textSecondary,
    accent: colors.app.accent,
    accentHover: colors.app.accentHover,
    cardBg: colors.app.surface,
    cardBorder: colors.app.surfaceLight,
    inputBg: colors.app.surfaceLight,
    inputBorder: colors.app.surfaceLighter,
    inputText: colors.app.textPrimary,
    buttonPrimaryBg: colors.app.accent,
    buttonPrimaryText: colors.app.textPrimary,
  },
};

/**
 * Typography Scale
 * Defines the typography scale for the application
 */
export const typography = {
  fontSizes: {
    xs: '0.75rem',     // 12px
    sm: '0.875rem',    // 14px
    base: '1rem',      // 16px
    lg: '1.125rem',    // 18px
    xl: '1.25rem',     // 20px
    '2xl': '1.5rem',   // 24px
    '3xl': '1.875rem', // 30px
    '4xl': '2.25rem',  // 36px
    '5xl': '3rem',     // 48px
    '6xl': '3.75rem',  // 60px
  },
  fontWeights: {
    normal: 400,
    medium: 500,
    semibold: 600,
    bold: 700,
    extrabold: 800,
    black: 900,
  },
  lineHeights: {
    none: 1,
    tight: 1.25,
    snug: 1.375,
    normal: 1.5,
    relaxed: 1.625,
    loose: 2,
  },
  letterSpacing: {
    tighter: '-0.05em',
    tight: '-0.025em',
    normal: '0',
    wide: '0.025em',
    wider: '0.05em',
    widest: '0.1em',
  },
};

// Export font objects for use in layout files
export {
  libreBaskerville,
  figTree,
  slackey,
  inter
}