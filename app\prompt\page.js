'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { onAuthStateChanged } from 'firebase/auth';
import { auth } from '@/lib/firebase';
import { ArrowLeft, BookOpen, Sparkles, Loader2 } from 'lucide-react';
import Link from 'next/link';

export default function PromptPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const chatId = searchParams.get('chatId');

  const [user, setUser] = useState(null);
  const [authLoading, setAuthLoading] = useState(true);
  const [formData, setFormData] = useState({
    whatIfPrompt: '',
    audioFile: null
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [retryCount, setRetryCount] = useState(0);

  // Auth listener
  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, (currentUser) => {
      setUser(currentUser);
      setAuthLoading(false);
      if (!currentUser) {
        router.push('/register');
      }
    });

    return () => unsubscribe();
  }, [router]);

  // Redirect if no chatId
  useEffect(() => {
    if (!chatId) {
      router.push('/dashboard');
    }
  }, [chatId, router]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleFileChange = (e) => {
    const file = e.target.files[0];
    setFormData(prev => ({
      ...prev,
      audioFile: file
    }));
  };

  const handleSubmit = async (e, isRetry = false) => {
    e.preventDefault();

    if (!formData.whatIfPrompt) {
      setError('Please provide a "What if" scenario');
      return;
    }

    setIsLoading(true);
    if (!isRetry) {
      setError('');
      setRetryCount(0);
    }

    try {
      // Create FormData for file upload
      const submitData = new FormData();
      submitData.append('whatIfPrompt', formData.whatIfPrompt);
      if (formData.audioFile) {
        submitData.append('audioFile', formData.audioFile);
      }

      // Generate story tree
      const response = await fetch('/api/generate-story-tree', {
        method: 'POST',
        body: submitData,
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));

        // Handle specific error cases
        if (response.status === 400) {
          throw new Error('Please provide a valid "What if" scenario.');
        } else if (response.status === 401) {
          throw new Error('Service configuration error. Please try again later.');
        } else if (response.status === 429) {
          throw new Error('Service is busy. Please wait a moment and try again.');
        } else if (response.status >= 500) {
          throw new Error('Story generation service is temporarily unavailable. Please try again in a few minutes.');
        } else {
          throw new Error(errorData.error || 'Failed to generate story. Please try again.');
        }
      }

      const storyData = await response.json();

      // Save story data to the chat
      const saveResponse = await fetch(`/api/chats/${chatId}/storyData`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(storyData),
      });

      if (!saveResponse.ok) {
        throw new Error('Failed to save story data');
      }

      // Update chat with simplified info
      await fetch(`/api/chats/${chatId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title: `What If Story`,
          bookInfo: { whatIfPrompt: formData.whatIfPrompt }
        }),
      });

      // Redirect to story reader
      router.push(`/story-reader/${chatId}`);

    } catch (error) {
      console.error('Error generating story:', error);

      // Determine if we should show retry option
      const canRetry = retryCount < 2 && (
        error.message.includes('temporarily unavailable') ||
        error.message.includes('Service is busy') ||
        error.message.includes('try again')
      );

      if (canRetry) {
        setError(`${error.message} (Attempt ${retryCount + 1} of 3)`);
        setRetryCount(prev => prev + 1);
      } else {
        // Provide helpful error messages based on the error type
        let userFriendlyMessage = error.message;

        if (error.message.includes('audio') || error.message.includes('transcription')) {
          userFriendlyMessage = 'There was an issue processing your audio file. Please try with a different audio file or continue without audio.';
        } else if (error.message.includes('network') || error.message.includes('fetch')) {
          userFriendlyMessage = 'Network connection issue. Please check your internet connection and try again.';
        } else if (error.message.includes('save') || error.message.includes('storage')) {
          userFriendlyMessage = 'Story was generated but could not be saved. Please try again.';
        }

        setError(userFriendlyMessage);
      }
    } finally {
      setIsLoading(false);
    }
  };

  // Retry function
  const handleRetry = () => {
    const fakeEvent = { preventDefault: () => {} };
    handleSubmit(fakeEvent, true);
  };

  if (authLoading) {
    return (
      <div className="min-h-screen bg-app-pattern flex items-center justify-center">
        <Loader2 className="w-8 h-8 animate-spin text-app-accent" />
      </div>
    );
  }

  if (!chatId || !user) {
    return null;
  }

  return (
    <div className="min-h-screen bg-app-pattern p-4">
      <div className="max-w-2xl mx-auto">
        {/* Header */}
        <div className="flex items-center mb-8">
          <Link
            href={`/chat/${chatId}`}
            className="flex items-center text-app-text-secondary hover:text-app-text-primary transition-colors mr-4"
          >
            <ArrowLeft className="w-5 h-5 mr-2" />
            Back to Chat
          </Link>
        </div>

        {/* Main Form */}
        <div className="bg-app-surface rounded-xl shadow-lg p-6 md:p-8">
          <div className="text-center mb-8">
            <div className="flex justify-center mb-4">
              <div className="p-3 bg-app-accent/20 rounded-full">
                <BookOpen className="w-8 h-8 text-app-accent" />
              </div>
            </div>
            <h1 className="text-3xl font-bold text-app-text-primary mb-2">
              Create Your What-If Story
            </h1>
            <p className="text-app-text-secondary">
              Describe your &ldquo;what if&rdquo; scenario and optionally upload an audio file for additional context
            </p>
          </div>

          <form onSubmit={handleSubmit} className="space-y-6">
            {/* What If Prompt */}
            <div>
              <label htmlFor="whatIfPrompt" className="block text-sm font-medium text-app-text-primary mb-2">
                What if... *
              </label>
              <textarea
                id="whatIfPrompt"
                name="whatIfPrompt"
                value={formData.whatIfPrompt}
                onChange={handleInputChange}
                placeholder="Describe your alternate scenario idea. For example: 'What if Harry Potter had been sorted into Slytherin instead of Gryffindor?' or 'What if Romeo and Juliet had met in a different city?'"
                rows={6}
                className="w-full px-4 py-3 bg-app-background border border-app-surface-light rounded-lg focus:ring-2 focus:ring-app-accent focus:border-transparent text-app-text-primary placeholder-app-text-secondary resize-none"
                disabled={isLoading}
              />
            </div>

            {/* Audio File Upload */}
            <div>
              <label htmlFor="audioFile" className="block text-sm font-medium text-app-text-primary mb-2">
                Audio File (Optional)
              </label>
              <div className="relative">
                <input
                  type="file"
                  id="audioFile"
                  name="audioFile"
                  accept=".mp3,.wav,.m4a,.aac,.ogg,.flac"
                  onChange={handleFileChange}
                  className="w-full px-4 py-3 bg-app-background border border-app-surface-light rounded-lg focus:ring-2 focus:ring-app-accent focus:border-transparent text-app-text-primary file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-app-accent file:text-app-text-primary hover:file:bg-app-accent-hover"
                  disabled={isLoading}
                />
              </div>
              <p className="text-xs text-app-text-secondary mt-2">
                Upload an audio file to provide additional context for your story. Supported formats: MP3, WAV, M4A, AAC, OGG, FLAC
              </p>
            </div>

            {/* Error Message */}
            {error && (
              <div className="p-4 bg-red-500/10 border border-red-500/20 rounded-lg">
                <p className="text-red-400 text-sm mb-3">{error}</p>
                {retryCount > 0 && retryCount < 3 && (
                  <button
                    onClick={handleRetry}
                    disabled={isLoading}
                    className="px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600 transition-colors text-sm disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Try Again
                  </button>
                )}
              </div>
            )}

            {/* Submit Button */}
            <button
              type="submit"
              disabled={isLoading}
              className="w-full flex items-center justify-center px-6 py-3 bg-app-accent text-app-text-primary rounded-lg hover:bg-app-accent-hover transition-colors font-medium disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? (
                <>
                  <Loader2 className="w-5 h-5 mr-2 animate-spin" />
                  Generating Your Story...
                </>
              ) : (
                <>
                  <Sparkles className="w-5 h-5 mr-2" />
                  Generate Story
                </>
              )}
            </button>
          </form>
        </div>
      </div>
    </div>
  );
}
