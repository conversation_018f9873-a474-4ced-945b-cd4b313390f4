'use client';

import { useState } from 'react';
import { ThemedButton } from '@/app/theme/ThemeProvider';
import AudioPlayer from './AudioPlayer';
import { ChevronLeft, ChevronRight, BookOpen, List, Settings, Volume2 } from 'lucide-react';

export default function StoryContent({
  storyData,
  currentQuestionIndex,
  onNavigate,
  className = ''
}) {
  const [showSectionList, setShowSectionList] = useState(false);
  const [voiceGender, setVoiceGender] = useState('FEMALE');
  const [showVoiceSettings, setShowVoiceSettings] = useState(false);

  if (!storyData || !storyData.questions || storyData.questions.length === 0) {
    return (
      <div className="text-center py-8">
        <BookOpen className="w-12 h-12 mx-auto text-gray-400 mb-4" />
        <p className="text-gray-600">No story content available.</p>
      </div>
    );
  }

  const currentQuestion = storyData.questions[currentQuestionIndex];
  const hasMultipleSections = storyData.questions.length > 1;
  const isFirstSection = currentQuestionIndex === 0;
  const isLastSection = currentQuestionIndex === storyData.questions.length - 1;

  const goToPrevious = () => {
    if (!isFirstSection) {
      onNavigate(currentQuestionIndex - 1);
    }
  };

  const goToNext = () => {
    if (!isLastSection) {
      onNavigate(currentQuestionIndex + 1);
    }
  };

  const goToSection = (index) => {
    onNavigate(index);
    setShowSectionList(false);
  };

  // Detect if content is likely Hindi
  const isHindiContent = currentQuestion && (
    (currentQuestion.title && /[\u0900-\u097F]/.test(currentQuestion.title)) ||
    (currentQuestion.question && /[\u0900-\u097F]/.test(currentQuestion.question))
  );

  return (
    <div className={`story-content ${className}`}>
      {/* Voice Settings for Hindi Content */}
      {isHindiContent && (
        <div className="mb-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Volume2 className="w-4 h-4 text-blue-600" />
              <span className="text-sm font-medium text-blue-800">हिंदी आवाज़ सेटिंग्स (Hindi Voice Settings)</span>
            </div>
            <ThemedButton
              variant="ghost"
              size="sm"
              onClick={() => setShowVoiceSettings(!showVoiceSettings)}
            >
              <Settings className="w-4 h-4" />
            </ThemedButton>
          </div>

          {showVoiceSettings && (
            <div className="mt-3 pt-3 border-t border-blue-200">
              <div className="flex items-center gap-4">
                <span className="text-sm text-blue-700">आवाज़ चुनें (Choose Voice):</span>
                <div className="flex gap-2">
                  <button
                    onClick={() => setVoiceGender('FEMALE')}
                    className={`px-3 py-1 text-xs rounded-full transition-colors ${
                      voiceGender === 'FEMALE'
                        ? 'bg-blue-600 text-white'
                        : 'bg-blue-100 text-blue-700 hover:bg-blue-200'
                    }`}
                  >
                    महिला (Female)
                  </button>
                  <button
                    onClick={() => setVoiceGender('MALE')}
                    className={`px-3 py-1 text-xs rounded-full transition-colors ${
                      voiceGender === 'MALE'
                        ? 'bg-blue-600 text-white'
                        : 'bg-blue-100 text-blue-700 hover:bg-blue-200'
                    }`}
                  >
                    पुरुष (Male)
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Navigation Header */}
      {hasMultipleSections && (
        <div className="flex items-center justify-between mb-6 p-4 bg-gray-50 rounded-lg">
          <ThemedButton
            variant="ghost"
            onClick={goToPrevious}
            disabled={isFirstSection}
            className="flex items-center"
          >
            <ChevronLeft className="w-4 h-4 mr-1" />
            Previous
          </ThemedButton>

          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-600">
              Section {currentQuestionIndex + 1} of {storyData.questions.length}
            </span>
            
            <ThemedButton
              variant="ghost"
              size="sm"
              onClick={() => setShowSectionList(!showSectionList)}
            >
              <List className="w-4 h-4" />
            </ThemedButton>
          </div>

          <ThemedButton
            variant="ghost"
            onClick={goToNext}
            disabled={isLastSection}
            className="flex items-center"
          >
            Next
            <ChevronRight className="w-4 h-4 ml-1" />
          </ThemedButton>
        </div>
      )}

      {/* Section List Dropdown */}
      {showSectionList && hasMultipleSections && (
        <div className="mb-6 p-4 bg-white border border-gray-200 rounded-lg shadow-sm">
          <h3 className="text-lg font-semibold text-gray-800 mb-3">Story Sections</h3>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2">
            {storyData.questions.map((question, index) => (
              <ThemedButton
                key={index}
                variant={index === currentQuestionIndex ? "primary" : "ghost"}
                size="sm"
                onClick={() => goToSection(index)}
                className="text-left justify-start truncate"
              >
                <span className="font-medium mr-2">{index + 1}.</span>
                {question.title || `Section ${index + 1}`}
              </ThemedButton>
            ))}
          </div>
        </div>
      )}

      {/* Story Title */}
      {currentQuestion.title && (
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-800 mb-4 leading-tight">
            {currentQuestion.title}
          </h1>
          
          <div className="border-l-4 border-blue-500 pl-4">
            <AudioPlayer
              text={currentQuestion.title}
              variant="secondary"
              size="sm"
              showProgress={false}
              voiceGender={voiceGender}
            />
          </div>
        </div>
      )}

      {/* Story Text Content */}
      {currentQuestion.question && (
        <div className="mb-8">
          <div className="prose prose-lg max-w-none mb-6">
            {currentQuestion.question.split('\n').map((paragraph, index) => (
              paragraph.trim() && (
                <p key={index} className="text-gray-700 leading-relaxed mb-4">
                  {paragraph}
                </p>
              )
            ))}
          </div>
          
          <div className="border-l-4 border-green-500 pl-4">
            <AudioPlayer
              text={currentQuestion.question}
              variant="primary"
              showProgress={true}
              voiceGender={voiceGender}
            />
          </div>
        </div>
      )}

      {/* Story Choices */}
      {currentQuestion.options && currentQuestion.options.length > 0 && (
        <div className="mb-8">
          <h2 className="text-xl font-semibold text-gray-800 mb-4 flex items-center">
            <span className="w-2 h-2 bg-blue-500 rounded-full mr-3"></span>
            Story Choices
          </h2>
          
          <div className="space-y-4">
            {currentQuestion.options.map((option, index) => (
              <div 
                key={index} 
                className="bg-gradient-to-r from-gray-50 to-gray-100 rounded-lg p-4 border border-gray-200 hover:shadow-md transition-shadow"
              >
                <div className="flex items-start gap-3">
                  <div className="flex-shrink-0 w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-medium">
                    {index + 1}
                  </div>
                  
                  <div className="flex-grow">
                    <p className="text-gray-700 mb-3 leading-relaxed">{option}</p>
                    
                    <AudioPlayer
                      text={option}
                      variant="ghost"
                      size="sm"
                      showProgress={false}
                      voiceGender={voiceGender}
                    />
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Read All Choices */}
          <div className="mt-4 pt-4 border-t border-gray-200">
            <AudioPlayer
              text={isHindiContent
                ? `कहानी के विकल्प: ${currentQuestion.options.join('. ')}`
                : `Story choices: ${currentQuestion.options.join('. ')}`
              }
              variant="secondary"
              size="sm"
              showProgress={false}
              voiceGender={voiceGender}
            />
          </div>
        </div>
      )}

      {/* Story Metadata */}
      {(currentQuestion.level !== undefined || currentQuestion.is_ending) && (
        <div className="mb-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
          <div className="flex items-center gap-4 text-sm text-blue-700">
            {currentQuestion.level !== undefined && (
              <span className="flex items-center">
                <span className="font-medium">Level:</span>
                <span className="ml-1">{currentQuestion.level}</span>
              </span>
            )}
            
            {currentQuestion.is_ending && (
              <span className="bg-blue-200 text-blue-800 px-2 py-1 rounded-full text-xs font-medium">
                Story Ending
              </span>
            )}
          </div>
        </div>
      )}

      {/* Book Information */}
      {storyData.bookInfo && (
        <div className="mt-8 p-6 bg-gray-50 rounded-lg border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">Story Information</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            {storyData.bookInfo.title && (
              <div>
                <span className="font-medium text-gray-600">Title:</span>
                <p className="text-gray-800">{storyData.bookInfo.title}</p>
              </div>
            )}
            
            {storyData.bookInfo.author && (
              <div>
                <span className="font-medium text-gray-600">Author:</span>
                <p className="text-gray-800">{storyData.bookInfo.author}</p>
              </div>
            )}
            
            {storyData.bookInfo.changeLocation && (
              <div className="md:col-span-2">
                <span className="font-medium text-gray-600">Setting:</span>
                <p className="text-gray-800">{storyData.bookInfo.changeLocation}</p>
              </div>
            )}
            
            {storyData.bookInfo.whatIfPrompt && (
              <div className="md:col-span-2">
                <span className="font-medium text-gray-600">What-If Scenario:</span>
                <p className="text-gray-800 italic">"{storyData.bookInfo.whatIfPrompt}"</p>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Navigation Footer */}
      {hasMultipleSections && (
        <div className="mt-8 pt-6 border-t border-gray-200">
          <div className="flex items-center justify-between">
            <ThemedButton
              variant="secondary"
              onClick={goToPrevious}
              disabled={isFirstSection}
              className="flex items-center"
            >
              <ChevronLeft className="w-4 h-4 mr-2" />
              Previous Section
            </ThemedButton>

            <span className="text-gray-600 text-sm">
              {currentQuestionIndex + 1} / {storyData.questions.length}
            </span>

            <ThemedButton
              variant="secondary"
              onClick={goToNext}
              disabled={isLastSection}
              className="flex items-center"
            >
              Next Section
              <ChevronRight className="w-4 h-4 ml-2" />
            </ThemedButton>
          </div>
        </div>
      )}
    </div>
  );
}
